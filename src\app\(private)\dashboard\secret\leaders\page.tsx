"use client";
import { useState, useEffect } from "react";
import { Card } from "@/components/ui/Card";
import { toast } from "react-toastify";
import Link from "next/link";
import {
  Users,
  Plus,
  Edit3,
  Trash2,
  Eye,
  Shield,
  Award,
  User,
  Home,
  Save,
  X
} from "react-feather";
import { getLeaderImageUrl } from "@/utils/imageUtils";

interface Leader {
  id: string;
  name: string;
  position: string | {
    _id: string;
    name: string;
    description?: string;
    level: number;
  };
  department: string | {
    _id: string;
    name: string;
  };
  bio?: string;
  experience?: string;
  education?: string;
  achievements: string[];
  phone?: string;
  email?: string;
  image?: string;
  order: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

interface Department {
  id: string;
  name: string;
}

const LeadersManagement = () => {  const [leaders, setLeaders] = useState<Leader[]>([]);
  const [loading, setLoading] = useState(true);
  const [sessionToken, setSessionToken] = useState<string>("");
  const [showDepartmentModal, setShowDepartmentModal] = useState(false);
  const [departments, setDepartments] = useState<Department[]>([]);
  const [newDepartment, setNewDepartment] = useState("");
  const [editingDepartment, setEditingDepartment] = useState<{id: string, name: string} | null>(null);

  useEffect(() => {
    const token = localStorage.getItem("sessionToken") || "";
    setSessionToken(token);
  }, []);

  useEffect(() => {
    if (!sessionToken) return;
    fetchLeaders();
  }, [sessionToken]);
  const fetchLeaders = async () => {
    try {
      setLoading(true);
      
      const response = await fetch("/api/admin/leaders", {
        headers: {
          "Authorization": `Bearer ${sessionToken}`,
        },
      });
      const result = await response.json();
      
      if (response.ok) {
        setLeaders(result.data || []);
        toast.success("Đã tải danh sách lãnh đạo thành công");
        // Load departments after leaders are loaded
        await fetchDepartments();
      } else {
        console.error("API Error:", result.message);
        toast.error(result.message || "Lỗi khi tải danh sách lãnh đạo");
        setLeaders([]);
      }
    } catch (error) {
      console.error("Error fetching leaders:", error);
      toast.error("Không thể kết nối đến server");
      setLeaders([]);
    } finally {
      setLoading(false);
    }
  };
  const fetchDepartments = async () => {
    try {
      // Get departments from existing leaders data
      const uniqueDepartments = [...new Set(leaders.map(l => {
        if (typeof l.department === 'string') {
          return l.department;
        }
        return l.department?.name;
      }).filter(Boolean))];
      const departmentList = uniqueDepartments.map((name, index) => ({
        id: index.toString(),
        name: name
      }));
      setDepartments(departmentList);
    } catch (error) {
      console.error("Error processing departments:", error);
    }
  };
  const addDepartment = async () => {
    if (!newDepartment.trim()) return;
    if (departments.some(dept => dept.name === newDepartment.trim())) {
      toast.error("Phòng ban này đã tồn tại!");
      return;
    }

    // Add department locally
    const newDept = {
      id: Date.now().toString(),
      name: newDepartment.trim()
    };
    setDepartments([...departments, newDept]);
    setNewDepartment("");
    toast.success("Đã thêm phòng ban mới!");
  };
  const updateDepartment = async (departmentId: string, newName: string) => {
    if (!newName.trim()) return;
    
    const currentDept = departments.find(d => d.id === departmentId);
    if (!currentDept) return;

    if (departments.some(dept => dept.name === newName.trim() && dept.id !== departmentId)) {
      toast.error("Tên phòng ban này đã tồn tại!");
      return;
    }

    // Update department locally
    setDepartments(departments.map(dept => 
      dept.id === departmentId ? { ...dept, name: newName.trim() } : dept
    ));
    setEditingDepartment(null);
    toast.success("Đã cập nhật tên phòng ban!");
    
    // Note: This only updates the department list, not the leaders' department field
    // For full functionality, you would need to update all leaders with the old department name
  };
  const deleteDepartment = async (departmentId: string) => {
    const department = departments.find(d => d.id === departmentId);
    if (!department) return;

    const leadersInDept = leaders.filter(l => {
      if (typeof l.department === 'string') {
        return l.department === department.name;
      }
      return l.department?.name === department.name;
    });
    if (leadersInDept.length > 0) {
      toast.error(`Không thể xóa! Còn ${leadersInDept.length} lãnh đạo thuộc phòng ban này.`);
      return;
    }
    
    if (!confirm(`Bạn có chắc muốn xóa phòng ban "${department.name}"?`)) return;

    // Delete department locally
    setDepartments(departments.filter(dept => dept.id !== departmentId));
    toast.success("Đã xóa phòng ban!");
  };

  const handleDelete = async (id: string, name: string) => {
    if (!confirm(`Bạn có chắc muốn xóa thông tin lãnh đạo "${name}"?`)) {
      return;
    }

    try {
      const response = await fetch(`/api/admin/leaders/${id}`, {
        method: "DELETE",
        headers: {
          "Authorization": `Bearer ${sessionToken}`,
        },
      });
      const result = await response.json();
      
      if (response.ok) {
        setLeaders(leaders.filter(leader => leader.id !== id));
        toast.success("Đã xóa thông tin lãnh đạo thành công");
      } else {
        throw new Error(result.message);
      }
    } catch (error) {
      console.error("Error deleting leader:", error);
      toast.error("Lỗi khi xóa thông tin lãnh đạo");
    }
  };

  const toggleActive = async (id: string) => {
    try {
      const response = await fetch(`/api/admin/leaders/${id}/toggle`, {
        method: "PATCH",
        headers: {
          "Authorization": `Bearer ${sessionToken}`,
        },
      });
      const result = await response.json();
      
      if (response.ok) {
        setLeaders(leaders.map(leader => 
          leader.id === id ? { ...leader, isActive: !leader.isActive } : leader
        ));
        toast.success("Đã cập nhật trạng thái thành công");
      } else {
        throw new Error(result.message);
      }
    } catch (error) {
      console.error("Error toggling leader status:", error);
      toast.error("Lỗi khi cập nhật trạng thái");
    }
  };

  const getPositionIcon = (position: string | { name: string } | null) => {
    let positionName = "";
    if (typeof position === 'string') {
      positionName = position.toLowerCase();
    } else if (position?.name) {
      positionName = position.name.toLowerCase();
    } else {
      return <User size={20} className="text-gray-600" />;
    }

    if (positionName.includes("chánh án")) return <Shield size={20} className="text-red-600" />;
    if (positionName.includes("phó")) return <Award size={20} className="text-blue-600" />;
    if (positionName.includes("thẩm phán")) return <Shield size={20} className="text-blue-600" />;
    return <User size={20} className="text-gray-600" />;
  };

  const getPositionColor = (position: string | { name: string } | null) => {
    let positionName = "";
    if (typeof position === 'string') {
      positionName = position.toLowerCase();
    } else if (position?.name) {
      positionName = position.name.toLowerCase();
    } else {
      return "bg-gray-100 text-gray-800";
    }

    if (positionName.includes("chánh án")) return "bg-red-100 text-red-800";
    if (positionName.includes("phó")) return "bg-blue-100 text-blue-800";
    if (positionName.includes("thẩm phán")) return "bg-green-100 text-green-800";
    return "bg-gray-100 text-gray-800";
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 flex items-center">
            <Users className="mr-3" size={32} />
            Quản Lý Giới Thiệu Lãnh Đạo Toà Án
          </h1>
          <p className="text-gray-600 mt-2">
            Quản lý thông tin giới thiệu các lãnh đạo tòa án
          </p>
        </div>        <div className="flex space-x-3">
          <Link
            href="/dashboard/secret/departments"
            className="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg flex items-center space-x-2 transition-colors"
          >
            <Home size={20} />
            <span>Quản lý phòng ban</span>
          </Link>
          <Link
            href="/dashboard/secret/leaders/add"
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg flex items-center space-x-2 transition-colors"
          >
            <Plus size={20} />
            <span>Thêm lãnh đạo mới</span>
          </Link>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Tổng số lãnh đạo</p>
              <p className="text-2xl font-bold text-gray-900">{leaders.length}</p>
            </div>
            <Users size={24} className="text-blue-600" />
          </div>
        </Card>
        <Card>
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Đang hoạt động</p>
              <p className="text-2xl font-bold text-green-600">
                {leaders.filter(l => l.isActive).length}
              </p>
            </div>
            <Eye size={24} className="text-green-600" />
          </div>
        </Card>
        <Card>
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Chánh án</p>
              <p className="text-2xl font-bold text-red-600">
                {leaders.filter(l => {
                  if (typeof l.position === 'string') {
                    return l.position.includes("Chánh án");
                  }
                  return l.position?.name?.includes("Chánh án");
                }).length}
              </p>
            </div>
            <Shield size={24} className="text-red-600" />
          </div>
        </Card>
        <Card>
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Phó Chánh án</p>
              <p className="text-2xl font-bold text-blue-600">
                {leaders.filter(l => {
                  if (typeof l.position === 'string') {
                    return l.position.includes("Phó");
                  }
                  return l.position?.name?.includes("Phó");
                }).length}
              </p>
            </div>
            <Award size={24} className="text-blue-600" />
          </div>
        </Card>
      </div>

      {/* Leaders List */}
      <Card>
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-gray-900">Danh sách lãnh đạo</h2>
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-500">
              {leaders.length} lãnh đạo
            </span>
          </div>
        </div>

        {loading ? (
          <div className="space-y-4">
            {Array.from({ length: 3 }).map((_, index) => (
              <div key={index} className="border rounded-lg p-6 animate-pulse">
                <div className="flex items-center space-x-4">
                  <div className="w-16 h-16 bg-gray-200 rounded-lg"></div>
                  <div className="flex-1">
                    <div className="h-4 bg-gray-200 rounded w-1/3 mb-2"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/4 mb-2"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                  </div>
                  <div className="flex space-x-2">
                    <div className="w-8 h-8 bg-gray-200 rounded"></div>
                    <div className="w-8 h-8 bg-gray-200 rounded"></div>
                    <div className="w-8 h-8 bg-gray-200 rounded"></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : leaders.length === 0 ? (
          <div className="text-center py-12">
            <Users size={48} className="mx-auto text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Chưa có lãnh đạo nào được giới thiệu
            </h3>
            <p className="text-gray-500 mb-6">
              Bắt đầu bằng cách thêm thông tin lãnh đạo đầu tiên
            </p>
            <Link
              href="/dashboard/secret/leaders/add"
              className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg inline-flex items-center space-x-2"
            >
              <Plus size={20} />
              <span>Thêm Lãnh Đạo Cần Giới Thiệu</span>
            </Link>
          </div>
        ) : (
          <div className="space-y-4">
            {leaders
              .sort((a, b) => a.order - b.order)
              .map((leader) => (
                <div
                  key={leader.id}
                  className={`border rounded-lg p-6 hover:shadow-md transition-shadow ${
                    !leader.isActive ? 'bg-gray-50 opacity-75' : 'bg-white'
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div className="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center overflow-hidden">
                        {leader.image ? (
                          <img
                            src={getLeaderImageUrl(leader.image)}
                            alt={leader.name}
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <User size={24} className="text-gray-400" />
                        )}
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-2">
                          <h3 className="text-lg font-semibold text-gray-900">
                            {leader.name}
                          </h3>
                          {getPositionIcon(leader.position)}
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPositionColor(leader.position)}`}>
                            {typeof leader.position === 'string' ? leader.position : (leader.position?.name || "Chưa có chức vụ")}
                          </span>
                          {!leader.isActive && (
                            <span className="px-2 py-1 bg-gray-100 text-gray-600 rounded-full text-xs">
                              Ẩn
                            </span>
                          )}
                        </div>
                        <p className="text-gray-600 mb-1">
                          {typeof leader.department === 'string' ? leader.department : (leader.department?.name || "Chưa có phòng ban")}
                        </p>
                        <p className="text-sm text-gray-500">
                          {leader.experience ? `${leader.experience} kinh nghiệm` : "Chưa cập nhật kinh nghiệm"} • {leader.education || "Chưa cập nhật học vấn"}
                        </p>
                        {leader.bio && (
                          <p className="text-sm text-gray-700 mt-2 line-clamp-2">
                            {leader.bio}
                          </p>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center space-x-2 ml-4">
                      <button
                        onClick={() => toggleActive(leader.id)}
                        className={`p-2 rounded-lg transition-colors ${
                          leader.isActive
                            ? 'bg-green-100 text-green-600 hover:bg-green-200'
                            : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                        }`}
                        title={leader.isActive ? 'Ẩn' : 'Hiển thị'}
                      >
                        <Eye size={16} />
                      </button>
                      <Link
                        href={`/dashboard/secret/leaders/edit/${leader.id}`}
                        className="p-2 bg-blue-100 text-blue-600 rounded-lg hover:bg-blue-200 transition-colors"
                        title="Chỉnh sửa"
                      >
                        <Edit3 size={16} />
                      </Link>
                      <button
                        onClick={() => handleDelete(leader.id, leader.name)}
                        className="p-2 bg-red-100 text-red-600 rounded-lg hover:bg-red-200 transition-colors"
                        title="Xóa"
                      >
                        <Trash2 size={16} />
                      </button>
                    </div>
                  </div>
                </div>
              ))}
          </div>
        )}
      </Card>

      {/* Department Management Modal */}
      {showDepartmentModal && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
            <div
              className="fixed inset-0 transition-opacity bg-black bg-opacity-50 backdrop-blur-sm"
              onClick={() => setShowDepartmentModal(false)}
            ></div>

            <div className="inline-block w-full max-w-2xl my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-2xl rounded-2xl">
              {/* Modal Header */}
              <div className="bg-gradient-to-br from-green-600 via-green-700 to-green-800 px-6 py-4 text-white">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-white/10 backdrop-blur-sm rounded-xl flex items-center justify-center">
                      <Home size={20} className="text-white" />
                    </div>
                    <div>
                      <h2 className="text-xl font-bold">Quản lý phòng ban</h2>
                      <p className="text-green-100 text-sm">Thêm, sửa, xóa các phòng ban</p>
                    </div>
                  </div>
                  <button
                    onClick={() => setShowDepartmentModal(false)}
                    className="text-white hover:text-green-200 transition-colors bg-white/10 backdrop-blur-sm rounded-xl p-2 hover:bg-white/20"
                  >
                    <X size={18} />
                  </button>
                </div>
              </div>

              {/* Modal Content */}
              <div className="px-6 py-6">
                {/* Add New Department */}
                <div className="mb-6 p-4 bg-gray-50 rounded-xl">
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">Thêm phòng ban mới</h3>
                  <div className="flex space-x-3">
                    <input
                      type="text"
                      value={newDepartment}
                      onChange={(e) => setNewDepartment(e.target.value)}
                      onKeyPress={(e) => e.key === 'Enter' && addDepartment()}
                      placeholder="Nhập tên phòng ban..."
                      className="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                    />
                    <button
                      onClick={addDepartment}
                      className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors"
                    >
                      <Plus size={16} />
                      <span>Thêm</span>
                    </button>
                  </div>
                </div>

                {/* Departments List */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">
                    Danh sách phòng ban ({departments.length})
                  </h3>
                  {departments.length === 0 ? (
                    <div className="text-center py-8 text-gray-500">
                      <Home size={48} className="mx-auto text-gray-400 mb-3" />
                      <p>Chưa có phòng ban nào</p>
                    </div>
                  ) : (                    <div className="space-y-3 max-h-96 overflow-y-auto">
                      {departments.map((dept, index) => (
                        <div key={dept.id} className="flex items-center justify-between p-3 bg-white border border-gray-200 rounded-lg">
                          {editingDepartment?.id === dept.id ? (
                            <div className="flex-1 flex items-center space-x-3">
                              <input
                                type="text"
                                value={editingDepartment.name}
                                onChange={(e) => setEditingDepartment({...editingDepartment, name: e.target.value})}
                                onKeyPress={(e) => {
                                  if (e.key === 'Enter') {
                                    updateDepartment(dept.id, editingDepartment.name);
                                  }
                                  if (e.key === 'Escape') {
                                    setEditingDepartment(null);
                                  }
                                }}
                                className="flex-1 px-3 py-2 border border-blue-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                autoFocus
                              />
                              <button
                                onClick={() => updateDepartment(dept.id, editingDepartment.name)}
                                className="bg-blue-600 hover:bg-blue-700 text-white p-2 rounded-lg transition-colors"
                              >
                                <Save size={14} />
                              </button>
                              <button
                                onClick={() => setEditingDepartment(null)}
                                className="bg-gray-300 hover:bg-gray-400 text-gray-700 p-2 rounded-lg transition-colors"
                              >
                                <X size={14} />
                              </button>
                            </div>
                          ) : (
                            <>
                              <div className="flex-1">
                                <div className="flex items-center space-x-3">
                                  <Home size={16} className="text-gray-500" />
                                  <span className="font-medium text-gray-900">{dept.name}</span>
                                  <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
                                    {leaders.filter(l => {
                                      if (typeof l.department === 'string') {
                                        return l.department === dept.name;
                                      }
                                      return l.department?.name === dept.name;
                                    }).length} lãnh đạo
                                  </span>
                                </div>
                              </div>
                              <div className="flex items-center space-x-2">
                                <button
                                  onClick={() => setEditingDepartment({id: dept.id, name: dept.name})}
                                  className="bg-blue-100 text-blue-600 p-2 rounded-lg hover:bg-blue-200 transition-colors"
                                  title="Sửa tên phòng ban"
                                >
                                  <Edit3 size={14} />
                                </button>
                                <button
                                  onClick={() => deleteDepartment(dept.id)}
                                  className="bg-red-100 text-red-600 p-2 rounded-lg hover:bg-red-200 transition-colors"
                                  title="Xóa phòng ban"
                                >
                                  <Trash2 size={14} />
                                </button>
                              </div>
                            </>
                          )}
                        </div>
                      ))}
                    </div>
                  )}
                </div>

                {/* Footer */}
                <div className="mt-6 pt-4 border-t border-gray-200 flex justify-end">
                  <button
                    onClick={() => setShowDepartmentModal(false)}
                    className="bg-gray-600 hover:bg-gray-700 text-white px-6 py-2 rounded-lg transition-colors"
                  >
                    Đóng
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default LeadersManagement;
