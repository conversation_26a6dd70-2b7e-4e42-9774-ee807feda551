// Enhanced error handling middleware
const handleError = (error, req, res, next) => {
  // Error handling without debug logging

  // Default error response
  let statusCode = error.status || error.statusCode || 500;
  let message = 'Internal Server Error';

  // Handle specific error types
  if (error.name === 'ValidationError') {
    statusCode = 400;
    message = 'Validation Error';
  } else if (error.name === 'CastError') {
    statusCode = 400;
    message = 'Invalid ID format';
  } else if (error.code === 11000) {
    statusCode = 409;
    message = 'Duplicate field value';
  } else if (error.name === 'JsonWebTokenError') {
    statusCode = 401;
    message = 'Invalid token';
  } else if (error.name === 'TokenExpiredError') {
    statusCode = 401;
    message = 'Token expired';
  } else if (error.name === 'MongoError' || error.name === 'MongoServerError') {
    statusCode = 500;
    message = 'Database error';
  } else if (statusCode === 404) {
    message = 'Resource not found';
  } else if (statusCode === 403) {
    message = 'Access forbidden';
  } else if (statusCode === 401) {
    message = 'Authentication required';
  } else if (statusCode === 429) {
    message = 'Too many requests';
  }

  // In production, don't expose error details
  const response = {
    success: false,
    message: message,
    ...(process.env.NODE_ENV === 'development' && { 
      error: error.message,
      stack: error.stack 
    })
  };

  res.status(statusCode).json(response);
};

// Async error wrapper
const asyncHandler = (fn) => (req, res, next) => {
  Promise.resolve(fn(req, res, next)).catch(next);
};

// 404 handler
const notFound = (req, res, next) => {
  const error = new Error(`Not Found - ${req.originalUrl}`);
  error.status = 404;
  next(error);
};

module.exports = {
  handleError,
  asyncHandler,
  notFound
};
