import { NextRequest, NextResponse } from "next/server";
import { CreateLeaderBodySchema } from "@/schemaValidations/leader.schema";

// GET - Get all leaders (admin only)
export async function GET(request: NextRequest) {
  try {
    const token = request.headers.get("authorization")?.replace("Bearer ", "");
    if (!token) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
    }

    const backendUrl = process.env.BACKEND_URL || "http://localhost:8000";
    
    const response = await fetch(`${backendUrl}/api/leaders`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${token}`,
      },
      cache: 'no-store'
    });

    const data = await response.json();

    if (!response.ok) {
      return NextResponse.json(
        { 
          success: false,
          message: data.message || "Lỗi server khi lấy danh sách lãnh đạo" 
        },
        { status: response.status }
      );
    }

    return NextResponse.json({
      success: true,
      data: data.data,
      message: data.message || "Lấy danh sách lãnh đạo thành công"
    });

  } catch (error) {
    console.error("Error in admin leaders GET:", error);
    return NextResponse.json(
      { success: false, message: "Không thể kết nối đến server" },
      { status: 500 }
    );
  }
}

// POST - Create new leader (admin only)
export async function POST(request: NextRequest) {
  try {
    const token = request.headers.get("authorization")?.replace("Bearer ", "");
    if (!token) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    
    // Validate request body
    const validatedData = CreateLeaderBodySchema.parse(body);

    const backendUrl = process.env.BACKEND_URL || "http://localhost:8000";
    
    const response = await fetch(`${backendUrl}/api/leaders`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${token}`,
      },
      body: JSON.stringify(validatedData),
    });

    const data = await response.json();

    if (!response.ok) {
      return NextResponse.json(
        { 
          success: false,
          message: data.message || "Lỗi server khi tạo lãnh đạo mới" 
        },
        { status: response.status }
      );
    }

    return NextResponse.json({
      success: true,
      data: data.data,
      message: data.message || "Tạo lãnh đạo mới thành công"
    }, { status: 201 });

  } catch (error) {
    if (error instanceof Error && error.name === "ZodError") {
      return NextResponse.json(
        { message: "Dữ liệu không hợp lệ", errors: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { message: "Không thể kết nối đến server" },
      { status: 500 }
    );
  }
}
