"use client";
import { useState, useEffect, useRef } from 'react';
import { Calendar, Search, Filter, Download, Upload, Plus, Edit, Trash2, Save, X, FileText, Eye } from 'react-feather';

export default function UserSchedulePage() {
  const [lichData, setLichData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [filterType, setFilterType] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [dateFilter, setDateFilter] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [lastUpdated, setLastUpdated] = useState(null);
  const [editingItem, setEditingItem] = useState(null);
  const [editFormData, setEditFormData] = useState({});
  const [showEditModal, setShowEditModal] = useState(false);
  const [showAddModal, setShowAddModal] = useState(false);
  const [newItemData, setNewItemData] = useState({
    ngayLich: '',
    buoiLich: 'Sáng',
    loaiAn: '',
    phongXu: '',
    nguyenDon: '',
    biDon: '',
    viecKien: '',
    hoTen: '',
    noiXu: ''
  });
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [sortField, setSortField] = useState('ngayLich');
  const [sortDirection, setSortDirection] = useState('asc');

  const modalRef = useRef(null);
  const addModalRef = useRef(null);

  // Fetch data from server
  const fetchDataFromServer = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/schedule');
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const result = await response.json();
      
      if (result && Array.isArray(result.items)) {
        setLichData(result.items);
        setLastUpdated(result.lastUpdated);
      } else {
        setLichData([]);
      }
      
      setError(null);
    } catch (err) {
      setError('Không thể tải dữ liệu từ server. Vui lòng thử lại sau.');
      setLichData([]);
    } finally {
      setLoading(false);
    }
  };

  // Save data to server
  const saveDataToServer = async (data) => {
    try {
      const response = await fetch('/api/schedule', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      
      if (result.success) {
        setLastUpdated(result.lastUpdated);
        return true;
      } else {
        throw new Error(result.error || 'Failed to save data');
      }
    } catch (err) {
      setError('Không thể lưu dữ liệu. Vui lòng thử lại.');
      return false;
    }
  };

  // Parse date for sorting
  const parseDate = (dateStr) => {
    if (!dateStr) return new Date(0);
    
    const parts = dateStr.split('-');
    if (parts.length === 3) {
      const day = parseInt(parts[0]);
      const month = parts[1];
      const year = parseInt(parts[2]);
      
      const monthMap = {
        'Jan': 0, 'Feb': 1, 'Mar': 2, 'Apr': 3, 'May': 4, 'Jun': 5,
        'Jul': 6, 'Aug': 7, 'Sep': 8, 'Oct': 9, 'Nov': 10, 'Dec': 11
      };
      
      const monthNum = monthMap[month] !== undefined ? monthMap[month] : parseInt(month) - 1;
      return new Date(year, monthNum, day);
    }
    
    return new Date(dateStr);
  };

  // Sort data
  const sortData = (data) => {
    return [...data].sort((a, b) => {
      let aValue = a[sortField];
      let bValue = b[sortField];
      
      if (sortField === 'ngayLich') {
        aValue = parseDate(aValue);
        bValue = parseDate(bValue);
      }
      
      if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;
      if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;
      return 0;
    });
  };

  // Filter data
  const filteredData = lichData.filter(item => {
    const matchesSearch = searchTerm === '' || 
      Object.values(item).some(value => 
        value && value.toString().toLowerCase().includes(searchTerm.toLowerCase())
      );
    
    const matchesType = filterType === 'all' || item.loaiAn === filterType;
    
    const matchesDate = dateFilter === '' || item.ngayLich.includes(dateFilter);
    
    return matchesSearch && matchesType && matchesDate;
  });

  const sortedData = sortData(filteredData);

  // Pagination
  const totalPages = Math.ceil(sortedData.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentData = sortedData.slice(startIndex, endIndex);

  // Handle sort
  const handleSort = (field) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  // Handle edit
  const handleEdit = (item, index) => {
    setEditingItem(index);
    setEditFormData({ ...item });
    setShowEditModal(true);
  };

  // Save edit
  const handleSaveEdit = async () => {
    if (editingItem !== null) {
      const newData = [...lichData];
      newData[editingItem] = editFormData;
      
      const success = await saveDataToServer(newData);
      if (success) {
        setLichData(newData);
        setEditingItem(null);
        setEditFormData({});
        setShowEditModal(false);
      }
    }
  };

  // Cancel edit
  const handleCancelEdit = () => {
    setEditingItem(null);
    setEditFormData({});
    setShowEditModal(false);
  };

  // Handle delete
  const handleDelete = async (index) => {
    if (window.confirm('Bạn có chắc chắn muốn xóa mục này?')) {
      const newData = lichData.filter((_, i) => i !== index);
      const success = await saveDataToServer(newData);
      if (success) {
        setLichData(newData);
      }
    }
  };

  // Handle add new item
  const handleAddNew = async () => {
    if (!newItemData.ngayLich || !newItemData.loaiAn) {
      alert('Vui lòng điền đầy đủ thông tin bắt buộc (Ngày lịch, Loại án)');
      return;
    }

    const newData = [...lichData, newItemData];
    const success = await saveDataToServer(newData);
    if (success) {
      setLichData(newData);
      setNewItemData({
        ngayLich: '',
        buoiLich: 'Sáng',
        loaiAn: '',
        phongXu: '',
        nguyenDon: '',
        biDon: '',
        viecKien: '',
        hoTen: '',
        noiXu: ''
      });
      setShowAddModal(false);
    }
  };

  // Export to CSV
  const exportToCSV = () => {
    const headers = ['Ngày lịch', 'Buổi lịch', 'Loại án', 'Phòng xử', 'Nguyên đơn', 'Bị đơn', 'Việc kiện', 'Họ tên', 'Nơi xử'];
    const csvContent = [
      headers.join(','),
      ...sortedData.map(item => [
        item.ngayLich,
        item.buoiLich,
        item.loaiAn,
        item.phongXu,
        item.nguyenDon,
        item.biDon,
        item.viecKien,
        item.hoTen,
        item.noiXu
      ].map(field => `"${field || ''}"`).join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `lich-xet-xu-${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // Load data when component mounts
  useEffect(() => {
    fetchDataFromServer();
  }, []);

  // Close modal when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (modalRef.current && !modalRef.current.contains(event.target)) {
        handleCancelEdit();
      }
      if (addModalRef.current && !addModalRef.current.contains(event.target)) {
        setShowAddModal(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 p-6">
        <div className="max-w-7xl mx-auto">
          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="animate-pulse">
              <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
              <div className="space-y-4">
                {[...Array(5)].map((_, i) => (
                  <div key={i} className="h-16 bg-gray-200 rounded"></div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
                <Calendar className="text-blue-600" size={28} />
                Lịch Xét Xử
              </h1>
              <p className="text-gray-600 mt-1">
                Quản lý và theo dõi lịch xét xử
                {lastUpdated && (
                  <span className="text-sm text-gray-500 ml-2">
                    (Cập nhật lần cuối: {new Date(lastUpdated).toLocaleString('vi-VN')})
                  </span>
                )}
              </p>
            </div>
            
            <div className="flex flex-wrap gap-2">
              <button
                onClick={() => setShowAddModal(true)}
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2"
              >
                <Plus size={16} />
                Thêm mới
              </button>
              <button
                onClick={exportToCSV}
                className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center gap-2"
              >
                <Download size={16} />
                Xuất CSV
              </button>
            </div>
          </div>
        </div>

        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <div className="flex items-center gap-2 text-red-800">
              <X size={16} />
              <span>{error}</span>
            </div>
          </div>
        )}

        {/* Filters */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <Search size={16} className="inline mr-1" />
                Tìm kiếm
              </label>
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Tìm kiếm..."
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <Filter size={16} className="inline mr-1" />
                Loại án
              </label>
              <select
                value={filterType}
                onChange={(e) => setFilterType(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="all">Tất cả</option>
                <option value="KTST">KTST</option>
                <option value="HCST">HCST</option>
                <option value="HTST">HTST</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <Calendar size={16} className="inline mr-1" />
                Lọc theo ngày
              </label>
              <input
                type="text"
                value={dateFilter}
                onChange={(e) => setDateFilter(e.target.value)}
                placeholder="VD: Nov-22"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Hiển thị
              </label>
              <select
                value={itemsPerPage}
                onChange={(e) => setItemsPerPage(Number(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value={10}>10 mục</option>
                <option value={25}>25 mục</option>
                <option value={50}>50 mục</option>
                <option value={100}>100 mục</option>
              </select>
            </div>
          </div>
        </div>

        {/* Data Table */}
        <div className="bg-white rounded-lg shadow-sm overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  {[
                    { key: 'ngayLich', label: 'Ngày lịch' },
                    { key: 'buoiLich', label: 'Buổi lịch' },
                    { key: 'loaiAn', label: 'Loại án' },
                    { key: 'phongXu', label: 'Phòng xử' },
                    { key: 'nguyenDon', label: 'Nguyên đơn' },
                    { key: 'biDon', label: 'Bị đơn' },
                    { key: 'viecKien', label: 'Việc kiện' },
                    { key: 'hoTen', label: 'Họ tên' },
                    { key: 'noiXu', label: 'Nơi xử' }
                  ].map((column) => (
                    <th
                      key={column.key}
                      onClick={() => handleSort(column.key)}
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 transition-colors"
                    >
                      <div className="flex items-center gap-1">
                        {column.label}
                        {sortField === column.key && (
                          <span className="text-blue-600">
                            {sortDirection === 'asc' ? '↑' : '↓'}
                          </span>
                        )}
                      </div>
                    </th>
                  ))}
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Thao tác
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {currentData.length === 0 ? (
                  <tr>
                    <td colSpan="10" className="px-6 py-12 text-center text-gray-500">
                      <div className="flex flex-col items-center gap-2">
                        <FileText size={48} className="text-gray-300" />
                        <span>Không có dữ liệu</span>
                      </div>
                    </td>
                  </tr>
                ) : (
                  currentData.map((item, index) => (
                    <tr key={startIndex + index} className="hover:bg-gray-50 transition-colors">
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{item.ngayLich}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{item.buoiLich}</td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          item.loaiAn === 'KTST' ? 'bg-blue-100 text-blue-800' :
                          item.loaiAn === 'HCST' ? 'bg-green-100 text-green-800' :
                          item.loaiAn === 'HTST' ? 'bg-purple-100 text-purple-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {item.loaiAn}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{item.phongXu}</td>
                      <td className="px-6 py-4 text-sm text-gray-900 max-w-xs truncate" title={item.nguyenDon}>
                        {item.nguyenDon}
                      </td>
                      <td className="px-6 py-4 text-sm text-gray-900 max-w-xs truncate" title={item.biDon}>
                        {item.biDon}
                      </td>
                      <td className="px-6 py-4 text-sm text-gray-900 max-w-xs truncate" title={item.viecKien}>
                        {item.viecKien}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{item.hoTen}</td>
                      <td className="px-6 py-4 text-sm text-gray-900 max-w-xs truncate" title={item.noiXu}>
                        {item.noiXu}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex items-center gap-2">
                          <button
                            onClick={() => handleEdit(item, lichData.indexOf(item))}
                            className="text-blue-600 hover:text-blue-900 transition-colors"
                            title="Chỉnh sửa"
                          >
                            <Edit size={16} />
                          </button>
                          <button
                            onClick={() => handleDelete(lichData.indexOf(item))}
                            className="text-red-600 hover:text-red-900 transition-colors"
                            title="Xóa"
                          >
                            <Trash2 size={16} />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
              <div className="flex-1 flex justify-between sm:hidden">
                <button
                  onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                  disabled={currentPage === 1}
                  className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Trước
                </button>
                <button
                  onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                  disabled={currentPage === totalPages}
                  className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Sau
                </button>
              </div>
              <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                  <p className="text-sm text-gray-700">
                    Hiển thị <span className="font-medium">{startIndex + 1}</span> đến{' '}
                    <span className="font-medium">{Math.min(endIndex, sortedData.length)}</span> trong{' '}
                    <span className="font-medium">{sortedData.length}</span> kết quả
                  </p>
                </div>
                <div>
                  <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                    <button
                      onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                      disabled={currentPage === 1}
                      className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Trước
                    </button>

                    {[...Array(totalPages)].map((_, i) => {
                      const page = i + 1;
                      if (
                        page === 1 ||
                        page === totalPages ||
                        (page >= currentPage - 2 && page <= currentPage + 2)
                      ) {
                        return (
                          <button
                            key={page}
                            onClick={() => setCurrentPage(page)}
                            className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                              currentPage === page
                                ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                                : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                            }`}
                          >
                            {page}
                          </button>
                        );
                      } else if (
                        page === currentPage - 3 ||
                        page === currentPage + 3
                      ) {
                        return (
                          <span
                            key={page}
                            className="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700"
                          >
                            ...
                          </span>
                        );
                      }
                      return null;
                    })}

                    <button
                      onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                      disabled={currentPage === totalPages}
                      className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Sau
                    </button>
                  </nav>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Edit Modal */}
        {showEditModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <div ref={modalRef} className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
              <div className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-gray-900">Chỉnh sửa lịch xét xử</h3>
                  <button
                    onClick={handleCancelEdit}
                    className="text-gray-400 hover:text-gray-600 transition-colors"
                  >
                    <X size={20} />
                  </button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Ngày lịch *</label>
                    <input
                      type="text"
                      value={editFormData.ngayLich || ''}
                      onChange={(e) => setEditFormData({...editFormData, ngayLich: e.target.value})}
                      placeholder="VD: 10-Nov-22"
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Buổi lịch</label>
                    <select
                      value={editFormData.buoiLich || 'Sáng'}
                      onChange={(e) => setEditFormData({...editFormData, buoiLich: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="Sáng">Sáng</option>
                      <option value="Chiều">Chiều</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Loại án *</label>
                    <select
                      value={editFormData.loaiAn || ''}
                      onChange={(e) => setEditFormData({...editFormData, loaiAn: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="">Chọn loại án</option>
                      <option value="KTST">KTST</option>
                      <option value="HCST">HCST</option>
                      <option value="HTST">HTST</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Phòng xử</label>
                    <input
                      type="text"
                      value={editFormData.phongXu || ''}
                      onChange={(e) => setEditFormData({...editFormData, phongXu: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>

                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 mb-1">Nguyên đơn</label>
                    <input
                      type="text"
                      value={editFormData.nguyenDon || ''}
                      onChange={(e) => setEditFormData({...editFormData, nguyenDon: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>

                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 mb-1">Bị đơn</label>
                    <input
                      type="text"
                      value={editFormData.biDon || ''}
                      onChange={(e) => setEditFormData({...editFormData, biDon: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>

                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 mb-1">Việc kiện</label>
                    <textarea
                      value={editFormData.viecKien || ''}
                      onChange={(e) => setEditFormData({...editFormData, viecKien: e.target.value})}
                      rows={3}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Họ tên</label>
                    <input
                      type="text"
                      value={editFormData.hoTen || ''}
                      onChange={(e) => setEditFormData({...editFormData, hoTen: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Nơi xử</label>
                    <input
                      type="text"
                      value={editFormData.noiXu || ''}
                      onChange={(e) => setEditFormData({...editFormData, noiXu: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                </div>

                <div className="flex justify-end gap-3 mt-6">
                  <button
                    onClick={handleCancelEdit}
                    className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
                  >
                    Hủy
                  </button>
                  <button
                    onClick={handleSaveEdit}
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2"
                  >
                    <Save size={16} />
                    Lưu
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Add Modal */}
        {showAddModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <div ref={addModalRef} className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
              <div className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-gray-900">Thêm lịch xét xử mới</h3>
                  <button
                    onClick={() => setShowAddModal(false)}
                    className="text-gray-400 hover:text-gray-600 transition-colors"
                  >
                    <X size={20} />
                  </button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Ngày lịch *</label>
                    <input
                      type="text"
                      value={newItemData.ngayLich}
                      onChange={(e) => setNewItemData({...newItemData, ngayLich: e.target.value})}
                      placeholder="VD: 10-Nov-22"
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Buổi lịch</label>
                    <select
                      value={newItemData.buoiLich}
                      onChange={(e) => setNewItemData({...newItemData, buoiLich: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="Sáng">Sáng</option>
                      <option value="Chiều">Chiều</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Loại án *</label>
                    <select
                      value={newItemData.loaiAn}
                      onChange={(e) => setNewItemData({...newItemData, loaiAn: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="">Chọn loại án</option>
                      <option value="KTST">KTST</option>
                      <option value="HCST">HCST</option>
                      <option value="HTST">HTST</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Phòng xử</label>
                    <input
                      type="text"
                      value={newItemData.phongXu}
                      onChange={(e) => setNewItemData({...newItemData, phongXu: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>

                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 mb-1">Nguyên đơn</label>
                    <input
                      type="text"
                      value={newItemData.nguyenDon}
                      onChange={(e) => setNewItemData({...newItemData, nguyenDon: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>

                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 mb-1">Bị đơn</label>
                    <input
                      type="text"
                      value={newItemData.biDon}
                      onChange={(e) => setNewItemData({...newItemData, biDon: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>

                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 mb-1">Việc kiện</label>
                    <textarea
                      value={newItemData.viecKien}
                      onChange={(e) => setNewItemData({...newItemData, viecKien: e.target.value})}
                      rows={3}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Họ tên</label>
                    <input
                      type="text"
                      value={newItemData.hoTen}
                      onChange={(e) => setNewItemData({...newItemData, hoTen: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Nơi xử</label>
                    <input
                      type="text"
                      value={newItemData.noiXu}
                      onChange={(e) => setNewItemData({...newItemData, noiXu: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                </div>

                <div className="flex justify-end gap-3 mt-6">
                  <button
                    onClick={() => setShowAddModal(false)}
                    className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
                  >
                    Hủy
                  </button>
                  <button
                    onClick={handleAddNew}
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2"
                  >
                    <Plus size={16} />
                    Thêm
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
