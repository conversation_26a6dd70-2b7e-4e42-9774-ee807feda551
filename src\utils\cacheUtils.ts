/**
 * Cache utilities for managing localStorage and API cache
 */

// Clear all setting-related cache
export const clearSettingCache = () => {
  try {
    localStorage.removeItem('siteSetting');
    localStorage.removeItem('siteMenus');
  } catch (error) {
    // Silent error handling
  }
};

// Clear all localStorage cache
export const clearAllCache = () => {
  try {
    localStorage.clear();
  } catch (error) {
    // Silent error handling
  }
};

// Force refresh settings from API
export const forceRefreshSettings = async () => {
  try {
    // Clear cache first
    clearSettingCache();
    
    // Force page reload to fetch fresh data
    if (typeof window !== 'undefined') {
      window.location.reload();
    }
  } catch (error) {
    // Error force refreshing settings handled silently
  }
};

// Check if cache is stale (older than specified minutes)
export const isCacheStale = (cacheKey: string, maxAgeMinutes: number = 30): boolean => {
  try {
    const cacheTimestamp = localStorage.getItem(`${cacheKey}_timestamp`);
    if (!cacheTimestamp) return true;
    
    const cacheTime = parseInt(cacheTimestamp);
    const now = Date.now();
    const maxAge = maxAgeMinutes * 60 * 1000; // Convert to milliseconds
    
    return (now - cacheTime) > maxAge;
  } catch (error) {
    console.error('❌ Error checking cache staleness:', error);
    return true; // Assume stale if error
  }
};

// Set cache with timestamp
export const setCacheWithTimestamp = (key: string, data: any) => {
  try {
    localStorage.setItem(key, JSON.stringify(data));
    localStorage.setItem(`${key}_timestamp`, Date.now().toString());
  } catch (error) {
    console.error('❌ Error setting cache with timestamp:', error);
  }
};

// Get cache if not stale
export const getCacheIfFresh = (key: string, maxAgeMinutes: number = 30): any | null => {
  try {
    if (isCacheStale(key, maxAgeMinutes)) {
      return null;
    }
    
    const cached = localStorage.getItem(key);
    return cached ? JSON.parse(cached) : null;
  } catch (error) {
    console.error('❌ Error getting fresh cache:', error);
    return null;
  }
};
