const mongoose = require("mongoose");
const { Schema } = mongoose;

const positionSchema = new Schema(
  {
    name: {
      type: String,
      required: true,
      trim: true
    },
    description: {
      type: String,
      default: "",
      trim: true
    },
    department: {
      type: Schema.Types.ObjectId,
      ref: "Department",
      required: true
    },
    level: {
      type: Number,
      default: 1, // 1 = thấp nhất, số càng cao quyền càng lớn
      min: 1,
      max: 10
    },
    permissions: [{
      type: String,
      enum: ["create", "read", "update", "delete", "manage_users", "manage_categories", "manage_posts", "view_analytics"]
    }],
    isActive: {
      type: Boolean,
      default: true
    },
    isDefault: {
      type: Boolean,
      default: false // Đánh dấu position mặc định không thể xóa
    },
    order: {
      type: Number,
      default: 0 // Thứ tự hiển thị trong phòng ban
    },
    createdBy: {
      type: Schema.Types.ObjectId,
      ref: "User"
    }
  },
  { timestamps: true }
);

// Compound index to ensure unique position names within each department
positionSchema.index({ name: 1, department: 1 }, { unique: true });

// Index for performance
positionSchema.index({ department: 1, isActive: 1, order: 1 });
positionSchema.index({ isActive: 1 });

// Static method để tạo positions mặc định cho một department
positionSchema.statics.createDefaultPositionsForDepartment = async function(departmentId, createdBy = null) {
  const defaultPositions = [
    {
      name: "Trưởng phòng",
      description: "Trưởng phòng ban",
      level: 8,
      permissions: ["create", "read", "update", "delete", "manage_posts", "view_analytics"],
      isDefault: true,
      order: 1,
      department: departmentId,
      createdBy
    },
    {
      name: "Phó trưởng phòng",
      description: "Phó trưởng phòng ban",
      level: 6,
      permissions: ["create", "read", "update", "manage_posts"],
      isDefault: true,
      order: 2,
      department: departmentId,
      createdBy
    },
    {
      name: "Chuyên viên",
      description: "Chuyên viên phòng ban",
      level: 4,
      permissions: ["create", "read", "update"],
      isDefault: true,
      order: 3,
      department: departmentId,
      createdBy
    },
    {
      name: "Nhân viên",
      description: "Nhân viên phòng ban",
      level: 2,
      permissions: ["read"],
      isDefault: true,
      order: 4,
      department: departmentId,
      createdBy
    }
  ];

  try {
    // Check if positions already exist for this department
    const existingPositions = await this.find({ department: departmentId });
    if (existingPositions.length === 0) {
      await this.insertMany(defaultPositions);
      console.log(`Created default positions for department ${departmentId}`);
    }
  } catch (error) {
    console.error("Error creating default positions:", error);
  }
};

// Instance method to check if position can be deleted
positionSchema.methods.canBeDeleted = function() {
  return !this.isDefault;
};

module.exports = mongoose.model("Position", positionSchema);