const http = require('http');
require('dotenv').config();
const { validateEnvironment, getSecureDefaults } = require('./api/middleware/environmentValidation');

// Validate environment before starting server
validateEnvironment();

// Get secure configuration defaults
const config = getSecureDefaults();

const port = config.PORT;
const app = require('./app');

const server = http.createServer(app);

// Server startup
server.listen(port, () => {
  // Server started silently
});

// Graceful shutdown handling
process.on('SIGTERM', () => {
  server.close(() => {
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  server.close(() => {
    process.exit(0);
  });
});
