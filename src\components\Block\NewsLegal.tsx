// components/Block/NewsLegal.tsx
"use client";

import React, { useEffect, useState } from 'react';
import Link from 'next/link';
import axios from 'axios';

interface LegalDocument {
  id: string;
  title: string;
  link: string;
  date: string;
  summary?: string;
}

interface NewsLegalProps {
  category?: {
    name: string;
    slug: string;
  };
}

const NewsLegal: React.FC<NewsLegalProps> = ({ category }) => {
  const [documents, setDocuments] = useState<LegalDocument[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [isSampleData, setIsSampleData] = useState<boolean>(false);

  useEffect(() => {
    const fetchLegalDocuments = async () => {
      try {
        setLoading(true);

        // Thêm timestamp để tránh cache
        const response = await axios.get(`/api/legal-documents?t=${Date.now()}`);

        if (response.data && response.data.success) {
          setDocuments(response.data.items || []);
          setIsSampleData(response.data.isSampleData || false);
        } else {
          throw new Error(response.data?.message || 'Không thể tải dữ liệu');
        }
      } catch (err: any) {
        setError(err.message || 'Đã xảy ra lỗi khi tải dữ liệu');
      } finally {
        setLoading(false);
      }
    };

    fetchLegalDocuments();
  }, []);

  if (loading) {
    return (
      <div className="bg-white dark:bg-gray-900 rounded-lg shadow-sm p-4 h-full">
        <h2 className="text-xl font-bold mb-4 text-gray-800 dark:text-gray-200 border-b pb-2">
          {category?.name || 'Văn bản pháp quy'}
        </h2>
        <div className="animate-pulse space-y-3">
          {[1, 2, 3, 4, 5].map((i) => (
            <div key={i} className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-full mb-2"></div>
          ))}
        </div>
      </div>
    );
  }

  if (error && !documents.length) {
    return (
      <div className="bg-white dark:bg-gray-900 rounded-lg shadow-sm p-4 h-full">
        <h2 className="text-xl font-bold mb-4 text-gray-800 dark:text-gray-200 border-b pb-2">
          {category?.name || 'Văn bản pháp quy'}
        </h2>
        <div className="bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 p-3 rounded-md">
          {error}
        </div>
      </div>
    );
  }

  if (documents.length === 0) {
    return (
      <div className="bg-white dark:bg-gray-900 rounded-lg shadow-sm p-4 h-full">
        <h2 className="text-xl font-bold mb-4 text-gray-800 dark:text-gray-200 border-b pb-2">
          {category?.name || 'Văn bản pháp quy'}
        </h2>
        <p className="text-gray-500 dark:text-gray-400">Không có văn bản pháp quy nào.</p>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-900 rounded-lg shadow-sm p-4 h-full">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-bold text-gray-800 dark:text-gray-200">
          {category?.name || 'Văn bản pháp quy'}
        </h2>
        {category?.slug && (
          <Link href={`/category/${category.slug}`} className="text-blue-600 dark:text-blue-400 text-sm hover:underline">
            Xem tất cả
          </Link>
        )}
      </div>
      
      {isSampleData && (
        <div className="mb-3 px-3 py-2 bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400 text-sm rounded">
          <p>Hiển thị dữ liệu mẫu. Không thể kết nối với CSDL văn bản pháp quy.</p>
        </div>
      )}
      
      <div className="divide-y divide-gray-200 dark:divide-gray-700">
        {documents.slice(0, 7).map((doc) => (
          <div key={doc.id} className="py-3">
            <a 
              href={doc.link} 
              target="_blank" 
              rel="noopener noreferrer"
              className="block hover:bg-gray-50 dark:hover:bg-gray-800 transition duration-150 rounded-md p-2 -mx-2"
            >
              <h3 className="font-medium text-gray-900 dark:text-gray-100 mb-1">
                {doc.title}
              </h3>
              <div className="flex flex-col text-sm text-gray-500 dark:text-gray-400">
                <span className="mr-2">{doc.date}</span>
                {doc.summary && (
                  <span className="text-xs text-gray-500 dark:text-gray-400 mt-1 line-clamp-2">
                    {doc.summary}
                  </span>
                )}
              </div>
            </a>
          </div>
        ))}
      </div>
      
      <div className="mt-4 pt-3 border-t border-gray-200 dark:border-gray-700 text-right">
        <a 
          href="https://vbpq.toaan.gov.vn" 
          target="_blank" 
          rel="noopener noreferrer"
          className="text-blue-600 dark:text-blue-400 text-sm hover:underline"
        >
          Truy cập Cổng thông tin
        </a>
      </div>
    </div>
  );
};

export default NewsLegal;