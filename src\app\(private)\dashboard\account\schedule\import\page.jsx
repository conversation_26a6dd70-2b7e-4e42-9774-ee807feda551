"use client";
import { useState, useRef } from 'react';
import { Upload, Download, FileText, AlertCircle, CheckCircle, X } from 'react-feather';

export default function ScheduleImportPage() {
  const [uploadStatus, setUploadStatus] = useState(null);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [previewData, setPreviewData] = useState([]);
  const [showPreview, setShowPreview] = useState(false);
  const [error, setError] = useState(null);
  
  const fileInputRef = useRef(null);

  // Handle file upload
  const handleFileUpload = async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    // Validate file type
    if (!file.name.endsWith('.csv') && !file.name.endsWith('.xlsx') && !file.name.endsWith('.xls')) {
      setError('Vui lòng chọn file CSV hoặc Excel (.xlsx, .xls)');
      return;
    }

    setIsUploading(true);
    setUploadProgress(0);
    setError(null);

    try {
      // Simulate upload progress
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + 10;
        });
      }, 200);

      // Read file content
      const text = await file.text();
      
      // Parse CSV
      const lines = text.split('\n').filter(line => line.trim());
      const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''));
      
      // Expected headers
      const expectedHeaders = ['Ngày lịch', 'Buổi lịch', 'Loại án', 'Phòng xử', 'Nguyên đơn', 'Bị đơn', 'Việc kiện', 'Họ tên', 'Nơi xử'];
      
      // Validate headers
      const isValidFormat = expectedHeaders.every(header => 
        headers.some(h => h.toLowerCase().includes(header.toLowerCase()) || header.toLowerCase().includes(h.toLowerCase()))
      );

      if (!isValidFormat) {
        throw new Error('Format file không đúng. Vui lòng kiểm tra lại các cột header.');
      }

      // Parse data
      const data = lines.slice(1).map(line => {
        const values = line.split(',').map(v => v.trim().replace(/"/g, ''));
        return {
          ngayLich: values[0] || '',
          buoiLich: values[1] || 'Sáng',
          loaiAn: values[2] || '',
          phongXu: values[3] || '',
          nguyenDon: values[4] || '',
          biDon: values[5] || '',
          viecKien: values[6] || '',
          hoTen: values[7] || '',
          noiXu: values[8] || ''
        };
      }).filter(item => item.ngayLich); // Filter out empty rows

      clearInterval(progressInterval);
      setUploadProgress(100);
      
      setTimeout(() => {
        setPreviewData(data);
        setShowPreview(true);
        setUploadStatus('success');
        setIsUploading(false);
      }, 500);

    } catch (err) {
      setError(err.message || 'Có lỗi xảy ra khi tải file');
      setUploadStatus('error');
      setIsUploading(false);
    }
  };

  // Save imported data
  const handleSaveImportedData = async () => {
    try {
      setIsUploading(true);
      
      const response = await fetch('/api/schedule', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(previewData),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      
      if (result.success) {
        setUploadStatus('saved');
        setShowPreview(false);
        setPreviewData([]);
        if (fileInputRef.current) {
          fileInputRef.current.value = '';
        }
      } else {
        throw new Error(result.error || 'Failed to save data');
      }
    } catch (err) {
      setError('Không thể lưu dữ liệu. Vui lòng thử lại.');
    } finally {
      setIsUploading(false);
    }
  };

  // Download template
  const downloadTemplate = () => {
    const headers = ['Ngày lịch', 'Buổi lịch', 'Loại án', 'Phòng xử', 'Nguyên đơn', 'Bị đơn', 'Việc kiện', 'Họ tên', 'Nơi xử'];
    const sampleData = [
      ['10-Nov-22', 'Sáng', 'KTST', '4', 'Ngân hàng TMCP Sài Gòn - Hà Nội', 'Công ty Cổ phần đầu tư Xây dựng Trí Dũng', 'Tranh chấp hợp đồng tín dụng', 'Trương Thị Quỳnh Trâm', '26 Lê Thánh Tôn, Quận 1'],
      ['15-Nov-22', 'Sáng', 'HCST', '6', 'Nguyễn Thị Thanh Hà', 'UBND huyện Củ Chi, TP.HCM', 'Khiếu kiện quyết định về cưỡng chế thu hồi đất', 'Nguyễn Minh Hiếu', '26 Lê Thánh Tôn, Quận 1']
    ];
    
    const csvContent = [
      headers.join(','),
      ...sampleData.map(row => row.map(field => `"${field}"`).join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', 'lich-xet-xu-template.csv');
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <h1 className="text-2xl font-bold text-gray-900 flex items-center gap-2 mb-2">
            <Upload className="text-blue-600" size={28} />
            Import Lịch Xét Xử
          </h1>
          <p className="text-gray-600">
            Tải lên file CSV hoặc Excel để import dữ liệu lịch xét xử
          </p>
        </div>

        {/* Status Messages */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <div className="flex items-center gap-2 text-red-800">
              <AlertCircle size={16} />
              <span>{error}</span>
            </div>
          </div>
        )}

        {uploadStatus === 'success' && !showPreview && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
            <div className="flex items-center gap-2 text-green-800">
              <CheckCircle size={16} />
              <span>File đã được tải lên thành công!</span>
            </div>
          </div>
        )}

        {uploadStatus === 'saved' && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
            <div className="flex items-center gap-2 text-green-800">
              <CheckCircle size={16} />
              <span>Dữ liệu đã được lưu thành công!</span>
            </div>
          </div>
        )}

        {/* Upload Section */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 mb-6">
            <div>
              <h2 className="text-lg font-semibold text-gray-900 mb-2">Tải lên file dữ liệu</h2>
              <p className="text-gray-600">
                Chọn file CSV hoặc Excel chứa dữ liệu lịch xét xử
              </p>
            </div>
            
            <button
              onClick={downloadTemplate}
              className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center gap-2"
            >
              <Download size={16} />
              Tải template
            </button>
          </div>

          {/* File Upload Area */}
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
            <input
              ref={fileInputRef}
              type="file"
              accept=".csv,.xlsx,.xls"
              onChange={handleFileUpload}
              className="hidden"
              disabled={isUploading}
            />
            
            {!isUploading ? (
              <div>
                <FileText size={48} className="text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600 mb-4">
                  Kéo thả file vào đây hoặc click để chọn file
                </p>
                <button
                  onClick={() => fileInputRef.current?.click()}
                  className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
                >
                  Chọn file
                </button>
                <p className="text-sm text-gray-500 mt-2">
                  Hỗ trợ: CSV, Excel (.xlsx, .xls)
                </p>
              </div>
            ) : (
              <div>
                <div className="w-16 h-16 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
                <p className="text-gray-600 mb-2">Đang xử lý file...</p>
                <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
                  <div 
                    className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${uploadProgress}%` }}
                  ></div>
                </div>
                <p className="text-sm text-gray-500">{uploadProgress}%</p>
              </div>
            )}
          </div>
        </div>

        {/* Preview Section */}
        {showPreview && (
          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold text-gray-900">
                Xem trước dữ liệu ({previewData.length} mục)
              </h2>
              <div className="flex gap-2">
                <button
                  onClick={() => {
                    setShowPreview(false);
                    setPreviewData([]);
                    setUploadStatus(null);
                    if (fileInputRef.current) {
                      fileInputRef.current.value = '';
                    }
                  }}
                  className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors flex items-center gap-2"
                >
                  <X size={16} />
                  Hủy
                </button>
                <button
                  onClick={handleSaveImportedData}
                  disabled={isUploading}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <CheckCircle size={16} />
                  {isUploading ? 'Đang lưu...' : 'Lưu dữ liệu'}
                </button>
              </div>
            </div>

            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Ngày lịch</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Buổi lịch</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Loại án</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Phòng xử</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Nguyên đơn</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Bị đơn</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Việc kiện</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Họ tên</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Nơi xử</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {previewData.slice(0, 10).map((item, index) => (
                    <tr key={index} className="hover:bg-gray-50">
                      <td className="px-4 py-3 text-sm text-gray-900">{item.ngayLich}</td>
                      <td className="px-4 py-3 text-sm text-gray-900">{item.buoiLich}</td>
                      <td className="px-4 py-3 text-sm text-gray-900">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          item.loaiAn === 'KTST' ? 'bg-blue-100 text-blue-800' :
                          item.loaiAn === 'HCST' ? 'bg-green-100 text-green-800' :
                          item.loaiAn === 'HTST' ? 'bg-purple-100 text-purple-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {item.loaiAn}
                        </span>
                      </td>
                      <td className="px-4 py-3 text-sm text-gray-900">{item.phongXu}</td>
                      <td className="px-4 py-3 text-sm text-gray-900 max-w-xs truncate" title={item.nguyenDon}>
                        {item.nguyenDon}
                      </td>
                      <td className="px-4 py-3 text-sm text-gray-900 max-w-xs truncate" title={item.biDon}>
                        {item.biDon}
                      </td>
                      <td className="px-4 py-3 text-sm text-gray-900 max-w-xs truncate" title={item.viecKien}>
                        {item.viecKien}
                      </td>
                      <td className="px-4 py-3 text-sm text-gray-900">{item.hoTen}</td>
                      <td className="px-4 py-3 text-sm text-gray-900 max-w-xs truncate" title={item.noiXu}>
                        {item.noiXu}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
              
              {previewData.length > 10 && (
                <div className="p-4 text-center text-gray-500">
                  ... và {previewData.length - 10} mục khác
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
