"use client";
import DraggableList from "@/components/Widget/DraggableList";
import { toast } from "react-toastify";
import { useRouter } from "next/navigation";
import settingApiRequest from "@/apiRequests/common";
import { handleApiError } from "@/utils/errorHandler";

export default function Menu() {
  const router = useRouter();
  const handleCreate = async (data: any) => {
    try { 
      const sessionToken = localStorage.getItem("sessionToken") || "";
      const result = await settingApiRequest.CraeteMenu(data, sessionToken);
      if (result.payload.success) {
        toast.success("Thành Công");
        router.push(`/dashboard/secret/menu/${result.payload.menu._id}`);
      } else {
        handleApiError(result, toast, "Có lỗi xảy ra khi tạo menu. Vui lòng thử lại.");
      }
    } catch (error) {
      handleApiError(error, toast, "Có lỗi xảy ra khi tạo menu. <PERSON>ui lòng thử lại.");
    }
  };
  return <DraggableList handleSubmit={handleCreate} />;
}
