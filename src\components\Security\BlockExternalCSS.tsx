"use client";

import { useEffect } from 'react';

const BlockExternalCSS = () => {
  useEffect(() => {
    // Function to remove ONLY specific unwanted CSS files
    const removeUnwantedCSS = () => {
      // Very selective removal - only target known problematic CSS
      const cssLinks = document.querySelectorAll('link[rel="stylesheet"]');

      cssLinks.forEach((link) => {
        const href = link.getAttribute('href') || '';

        // ONLY remove CSS from specific problematic libraries
        // Whitelist: Allow ALL Next.js CSS (_next/static/css/*)
        if (
          href.includes('/node_modules/hogan.js/') ||
          href.includes('/hogan.js/build/') ||
          (href.includes('hogan') && href.includes('layout.css'))
        ) {
          link.remove();
        }
      });
    };

    // Function to override ::marker styles
    const overrideMarkerStyles = () => {
      // Create style element to override ::marker
      const style = document.createElement('style');
      style.textContent = `
        ::marker {
          unicode-bidi: normal !important;
          font-variant-numeric: normal !important;
          text-transform: inherit !important;
          text-indent: inherit !important;
          text-align: inherit !important;
          text-align-last: inherit !important;
          content: none !important;
          display: none !important;
        }
        
        ul, ol {
          list-style: none !important;
        }
        
        ul::marker, ol::marker, li::marker {
          content: none !important;
          display: none !important;
        }
      `;
      
      document.head.appendChild(style);
    };

    // Function to monitor and block new CSS injections
    const blockNewCSS = () => {
      const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === 1) { // Element node
              const element = node as Element;

              // Check if it's a link element with unwanted CSS
              if (element.tagName === 'LINK' && element.getAttribute('rel') === 'stylesheet') {
                const href = element.getAttribute('href') || '';

                // ONLY block specific problematic CSS, NEVER Next.js CSS
                if (
                  href.includes('/node_modules/hogan.js/') ||
                  href.includes('/hogan.js/build/') ||
                  (href.includes('hogan') && href.includes('layout.css') && !href.includes('_next'))
                ) {
                  element.remove();
                }
              }

              // Check for style elements with unwanted content from external libraries
              if (element.tagName === 'STYLE') {
                const content = element.textContent || '';
                if (
                  content.includes('::marker') &&
                  content.includes('unicode-bidi') &&
                  content.includes('tabular-nums') // Only block user agent styles
                ) {
                  element.remove();
                }
              }
            }
          });
        });
      });

      observer.observe(document.head, {
        childList: true,
        subtree: true
      });

      return observer;
    };

    // Execute all blocking functions
    removeUnwantedCSS();
    overrideMarkerStyles();
    const observer = blockNewCSS();

    // Run removal check periodically
    const interval = setInterval(removeUnwantedCSS, 1000);

    // Cleanup
    return () => {
      clearInterval(interval);
      observer.disconnect();
    };
  }, []);

  return null;
};

export default BlockExternalCSS;
