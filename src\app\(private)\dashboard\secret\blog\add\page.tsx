"use client";
import AddForm from "@/app/(private)/dashboard/secret/blog/add/add-form";
import blogApiRequest from "@/apiRequests/blog";
import { BlogCreateType } from "@/schemaValidations/blog.schema"; 
import { toast } from "react-toastify";
import { useRouter } from "next/navigation";
import { handleApiError } from "@/utils/errorHandler";



const AddBlog = () => {
  const router = useRouter();
  const handleCreate = async (data:  BlogCreateType) => {
  
    try {
      const sessionToken = localStorage.getItem("sessionToken") || "";
      const result = await blogApiRequest.createBlog(data, sessionToken);
      if (result.payload.success) {
        toast.success("Thành Công");
        router.push(`/dashboard/secret/blog/${result.payload.post._id}`);
      } else {
        handleApiError(result, toast, "Có lỗi xảy ra khi tạo blog. Vui lòng thử lại.");
        console.error("Error creating Blog:", result.payload.message);
      }
    } catch (error) {
      console.error("Unexpected error:", error);
      handleApiError(error, toast, "Có lỗi xảy ra khi tạo blog. Vui lòng thử lại.");
    }
  };
    return (
      <>
        <h1 className="text-2xl"> Blog</h1>
        <AddForm onSubmit={handleCreate} />
      </>
    );
  };
  
  export default AddBlog;
  