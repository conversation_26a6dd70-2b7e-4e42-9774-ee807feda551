const express = require("express");
const router = express.Router();
const passport = require("passport");
const { body, param, validationResult } = require("express-validator");
const positionController = require("../controllers/position");
const { verifyAdmin } = require("../middleware/is-admin");

// Middleware to handle validation errors
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: "<PERSON><PERSON> liệu không hợp lệ",
      errors: errors.array()
    });
  }
  next();
};

// Validation rules for creating position
const createPositionValidation = [
  body("name")
    .trim()
    .notEmpty()
    .withMessage("Tên chức vụ không được để trống")
    .isLength({ min: 2, max: 100 })
    .withMessage("Tên chức vụ phải từ 2-100 ký tự"),
  body("departmentId")
    .isMongoId()
    .withMessage("Department ID không hợp lệ"),
  body("description")
    .optional()
    .trim()
    .isLength({ max: 500 })
    .withMessage("Mô tả không được quá 500 ký tự"),
  body("level")
    .optional()
    .isInt({ min: 1, max: 10 })
    .withMessage("Level phải từ 1-10"),
  body("permissions")
    .optional()
    .isArray()
    .withMessage("Permissions phải là array"),
  body("order")
    .optional()
    .isInt({ min: 0 })
    .withMessage("Order phải là số nguyên >= 0")
];

// Validation rules for updating position
const updatePositionValidation = [
  body("name")
    .optional()
    .trim()
    .notEmpty()
    .withMessage("Tên chức vụ không được để trống")
    .isLength({ min: 2, max: 100 })
    .withMessage("Tên chức vụ phải từ 2-100 ký tự"),
  body("description")
    .optional()
    .trim()
    .isLength({ max: 500 })
    .withMessage("Mô tả không được quá 500 ký tự"),
  body("level")
    .optional()
    .isInt({ min: 1, max: 10 })
    .withMessage("Level phải từ 1-10"),
  body("permissions")
    .optional()
    .isArray()
    .withMessage("Permissions phải là array"),
  body("order")
    .optional()
    .isInt({ min: 0 })
    .withMessage("Order phải là số nguyên >= 0")
];

// Routes

// GET /api/positions - Lấy tất cả positions
router.get(
  "/",
  passport.authenticate("user", { session: false }),
  verifyAdmin,
  positionController.getAllPositions
);

// GET /api/positions/permissions - Lấy danh sách permissions
router.get(
  "/permissions",
  passport.authenticate("user", { session: false }),
  verifyAdmin,
  positionController.getAvailablePermissions
);

// GET /api/positions/department/:departmentId - Lấy positions theo department
router.get(
  "/department/:departmentId",
  passport.authenticate("user", { session: false }),
  verifyAdmin,
  param("departmentId").isMongoId().withMessage("Department ID không hợp lệ"),
  handleValidationErrors,
  positionController.getPositionsByDepartment
);

// GET /api/positions/:id - Lấy position theo ID
router.get(
  "/:id",
  passport.authenticate("user", { session: false }),
  verifyAdmin,
  param("id").isMongoId().withMessage("ID không hợp lệ"),
  handleValidationErrors,
  positionController.getPositionById
);

// POST /api/positions - Tạo position mới
router.post(
  "/",
  passport.authenticate("user", { session: false }),
  verifyAdmin,
  ...createPositionValidation,
  handleValidationErrors,
  positionController.createPosition
);

// PUT /api/positions/:id - Cập nhật position
router.put(
  "/:id",
  passport.authenticate("user", { session: false }),
  verifyAdmin,
  param("id").isMongoId().withMessage("ID không hợp lệ"),
  ...updatePositionValidation,
  handleValidationErrors,
  positionController.updatePosition
);

// DELETE /api/positions/:id - Xóa position
router.delete(
  "/:id",
  passport.authenticate("user", { session: false }),
  verifyAdmin,
  param("id").isMongoId().withMessage("ID không hợp lệ"),
  handleValidationErrors,
  positionController.deletePosition
);

module.exports = router;