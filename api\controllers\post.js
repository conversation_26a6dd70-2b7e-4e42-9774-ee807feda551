const mongoose = require("mongoose");
const Post = require("../models/post");
const { ForbiddenError } = require("@casl/ability");
const defineAbilityFor = require("../permissions/abilities");
const PostCat = require("../models/post_cat");
const config = require("../config/index");
const User = require("../models/user");
const fs = require("fs");
const path = require("path");
const url = require("url");
const Video = require("../models/video");

exports.getHomePosts = async (req, res) => {
  try {
    const cat = await PostCat.find({ isFeature: true }).sort({ index: "desc" });

    const allPostPerCat = [];

    for (const category of cat) {
      const posts = await Post.find({
        categories: category._id,
        isActive: true,
      })
        .select("-desc -revisions")
        .populate({
          path: "categories",
          select: "name iconImg block", // Only include name and imgIcon
        })
        .limit(10)
        .sort({ createdAt: "desc" });

      allPostPerCat.push({
        id: category._id,
        name: category.name,
        slug: category.slug,
        index: category.index,
        block: category.block,
        iconImg: category.iconImg,
        posts: posts,
      });
    }

    // Sort the final array by `index` in descending order
    allPostPerCat.sort((a, b) => a.index - b.index);

    const newBlogs = await Post.aggregate([
      { $match: { isActive: true, isFeature: true } },
      { $sort: { index: -1 } },
      { $limit: 9 },
      {
        $lookup: {
          from: "postcats",
          localField: "categories",
          foreignField: "_id",
          as: "categories",
        },
      },
      {
        $project: {
          desc: 0,
          revisions: 0,
          "categories.slug": 0, // Optionally exclude unwanted fields
          "categories.index": 0,
          "categories.isFeature": 0,
          "categories.__v": 0,
        },
      },
    ]);
    res.status(200).json({
      message: "this is all posts",
      success: true,
      allPostPerCat,
      newBlogs,
    });
  } catch (err) {
    res.status(500).json({
      message: "Cant get posts",
      error: err,
      success: false,
    });
  }
};

exports.getHomePostsNew = async (req, res) => {
  try {
    const onPost = await Post.find()
      .sort({ createdAt: "desc" })
      .select("featureImg title short slug createdAt")
      .skip() // Trong post đầu tiên sẽ bỏ qua giá trị là 0
      .limit(3);
    const onPages = Page.find({ isFeature: true })
      .sort({ createdAt: "desc" })
      .limit(3);
    const [posts, pages] = await Promise.all([onPost, onPages]);
    res.status(200).json({
      message: "this is all posts",
      success: true,
      posts,
      pages,
    });
  } catch (err) {
    res.status(500).json({
      message: "Cant get posts",
      error: err,
      success: false,
    });
  }
};
exports.postByCat = async (req, res) => {
  const { cat } = req.params;
  try {
    const perPage = parseInt(req.query.perPage) || 20;
    const page = parseInt(req.query.page) || 1;

    const catId = await PostCat.findOne({ slug: cat });
    if (!catId) {
      return res.status(404).json({
        success: false,
        message: "Category not found",
      });
    }

    const total = await Post.countDocuments({
      $and: [{ isActive: true }, { categories: catId._id }],
    });

    const posts = await Post.find({
      $and: [{ isActive: true }, { categories: catId._id }],
    })
      .select("-desc -revisions")
      .populate("categories")
      .sort({ createdAt: -1 }) // Use -1 for descending order
      .skip(perPage * (page - 1))
      .limit(perPage);
    const newBlogs = await Post.aggregate([
      { $match: { isActive: true } },
      { $sort: { createdAt: -1 } },
      { $limit: 10 },
      {
        $lookup: {
          from: "postcats", // Correct collection name for PostCat
          localField: "categories",
          foreignField: "_id",
          as: "categories",
        },
      },
      {
        $project: {
          desc: 0,
          revisions: 0,
          "categories.slug": 0, // Optionally exclude unwanted fields
          "categories.index": 0,
          "categories.isFeature": 0,
          "categories.__v": 0,
        },
      },
    ]);
    res.status(200).json({
      success: true,
      total, // Corrected total count
      posts,
      catId,
      blogs: posts, // Duplicate of `posts`, consider removing
      newBlogs,
      catId,
    });
  } catch (err) {
    console.error("Error fetching posts by category:", err);
    res.status(500).json({
      success: false,
      message: "Can't get products",
      error: err.message,
    });
  }
};

exports.postByUser = async (req, res) => {
  const { _id } = req.user;
  try {
    const perPage = parseInt(req.query.perPage) || 20;
    const page = parseInt(req.query.page) || 1;

    const total = await Post.countDocuments({
      $and: [{ user: _id }],
    });

    const posts = await Post.find({
      $and: [{ user: _id }],
    })
      .select("-desc -revisions")
      .sort({ createdAt: -1 }) // Use -1 for descending order
      .skip(perPage * (page - 1))
      .limit(perPage)
      .populate("categories");
    res.status(200).json({
      success: true,
      total, // Corrected total count
      posts,
      blogs: posts, // Duplicate of `posts`, consider removing
    });
  } catch (err) {
    console.error("Error fetching posts by category:", err);
    res.status(500).json({
      success: false,
      message: "Can't get products",
      error: err.message,
    });
  }
};

exports.postByManager = async (req, res) => {
  const { _id } = req.user;
  try {
    const user = await User.findById({ _id });
    const perPage = parseInt(req.query.perPage) || 20;
    const page = parseInt(req.query.page) || 1;

    // Fetch total count of posts that belong to at least one of the user's categories
    const total = await Post.countDocuments({
      categories: { $in: user.categories }, // Matches posts that have at least one category from the user
    });

    // Fetch posts
    const posts = await Post.find({
      categories: { $in: user.categories },
    })
      .sort({ createdAt: -1 }) // Sort newest first
      .skip(perPage * (page - 1))
      .limit(perPage)
      .populate("categories");

    res.status(200).json({
      success: true,
      total,
      posts, // Removed the redundant `blogs`
    });
  } catch (err) {
    console.error("Error fetching posts by category:", err);
    res.status(500).json({
      success: false,
      message: "Can't get posts",
      error: err.message,
    });
  }
};

exports.getPosts = async (req, res) => {
  const { perPage = 20, page = 1, user, q, category } = req.body; // Extract from body
  const { feature } = req.query; // Extract feature from query parameters

  try {
    const query = {};

    if (user) {
      query.user = user; // Filter by user if provided
    }

    if (q) {
      query.$or = [
        { title: { $regex: q, $options: "i" } }, // Case-insensitive search in title
        { content: { $regex: q, $options: "i" } }, // Case-insensitive search in content
      ];
    }

    if (feature === "feature") {
      query.isFeature = true; // Filter by isFeature if feature param is in query
    }

    if (category) {
      // Find category by slug and get its ObjectId
      const categoryDoc = await PostCat.findOne({ slug: category }).select(
        "_id"
      );

      if (categoryDoc) {
        query.categories = categoryDoc._id; // Filter posts with this category
      } else {
        return res.status(404).json({
          message: "Category not found",
          success: false,
        });
      }
    }

    const total = await Post.countDocuments(query); // Count filtered posts
    const posts = await Post.find(query)
      .populate("categories", "name slug")
      .populate("user", "username _id")
      .sort({ index: -1, createdAt: "desc" }) // Sort by index (desc), then by createdAt (desc)
      .skip(perPage * (page - 1)) // Correct pagination calculation
      .limit(perPage);

    res.status(200).json({
      message: "Fetched posts successfully",
      total,
      posts,
      success: true,
    });
  } catch (err) {
    res.status(500).json({
      message: "Cannot fetch posts",
      error: err.message,
      success: false,
    });
  }
};

exports.createpost = async (req, res) => {
  try {
    const {
      title,
      desc,
      categories,
      isActive,
      index,
      short,
      featureImg,
      isFeature,
      slug,
      file,
    } = req.body;
    const post = new Post({
      title,
      desc,
      user: req.user._id,
      isFeature,
      isActive,
      index,
      short,
      slug,
      featureImg,
      file,
    });
    // post.featureImg = null
    // post.featureImg = featureImg
    if (Array.isArray(categories)) {
      post.categories = categories.map((id) => ({ _id: id }));
    } else if (categories && typeof categories === 'object') {
      // Handle object with numeric keys like { '0': 'id1', '1': 'id2' }
      const categoriesArray = Object.values(categories);
      post.categories = categoriesArray.map((id) => ({ _id: id }));
    } else if (categories) {
      post.categories = [{ _id: categories }];
    }
    await post.save();
    res.status(200).json({
      success: true,
      post,
    });
  } catch (err) {
    res.status(500).json({
      success: false,
      message: err,
    });
  }
};

exports.userCreatepost = async (req, res) => {
  const ability = defineAbilityFor(req.user);
  try {
    const { title, desc, categories, short, featureImg, slug, file } = req.body;
    const post = new Post({
      title,
      desc,
      user: req.user._id,
      short,
      slug,
      featureImg,
      file: file || [], // Ensure file is always an array
      index: 0,
    });
    // post.featureImg = null
    // post.featureImg = featureImg
    console.log(req.user.rule);
    if (
      req.user.rule === "editor" ||
      req.user.rule === "manager" ||
      req.user.rule === "admin"
    ) {
      post.isActive = true;
    } else {
      post.isActive = false;
    }
    if (Array.isArray(categories)) {
      post.categories = categories.map((id) => ({ _id: id }));
    } else if (categories && typeof categories === 'object') {
      // Handle object with numeric keys like { '0': 'id1', '1': 'id2' }
      const categoriesArray = Object.values(categories);
      post.categories = categoriesArray.map((id) => ({ _id: id }));
    } else if (categories) {
      post.categories = [{ _id: categories }];
    }
    ForbiddenError.from(ability).throwUnlessCan("update", post);

    await post.save();

    res.status(200).json({
      success: true,
      post,
    });
  } catch (err) {
    console.log(err);
    res.status(500).json({
      success: false,
      message: err,
    });
  }
};

exports.adminGetpost = async (req, res) => {
  try {
    const id = req.params.id;
    const post = await Post.findOne({ _id: id })
      .populate("user", "username rule") // Populate post owner
      .populate("revisions.user", "username"); // Populate username in revisions

    res.status(200).json({
      success: true,
      post,
    });
  } catch (err) {
    res.status(500).json({
      message: "Post not found",
    });
  }
};

exports.userGetpost = async (req, res) => {
  const ability = defineAbilityFor(req.user);
  try {
    const id = await req.params.id;
    
    // First, find the post without population to check permissions
    const post = await Post.findOne({ _id: id });
    if (!post) {
      return res.status(404).json({
        success: false,
        message: "Post not found",
      });
    }
    
    // Check permissions on the unpopulated post (where user is still ObjectId)
    ForbiddenError.from(ability).throwUnlessCan("read", post);
    
    // If permission check passes, get the populated post
    const populatedPost = await Post.findOne({ _id: id })
      .populate('user')
      .populate('categories')
      .populate('revisions.user');
      
    res.status(200).json({
      success: true,
      post: populatedPost,
    });
  } catch (err) {
    console.log(err);
    res.status(500).json({
      message: "post not found",
    });
  }
};

// lay san pham theo id
exports.postsGetpost = async (req, res) => {
  try {
    const slug = req.params.id;

    // Find the post by slug and populate categories and user
    const post = await Post.findOne({ slug, isActive: true })
      .populate("categories")
      .populate("user", "username");

    if (!post) {
      return res.status(404).json({ message: "Post not found" });
    }

    // Ensure there are categories in the post
    const categoryIds = post.categories.map((cat) => cat._id);

    // Fetch related blogs
    const blogs = await Post.aggregate([
      {
        $match: {
          categories: { $in: categoryIds }, // Match any category from the post
          _id: { $ne: post._id }, // Exclude the current post
          isActive: true,
        },
      },
      { $sort: { createdAt: -1 } },
      { $limit: 5 },
      {
        $lookup: {
          from: "postcats", // Correct collection name for PostCat
          localField: "categories",
          foreignField: "_id",
          as: "categories",
        },
      },
    ]);

    // Fetch latest blogs excluding the current post
    const newBlogs = await Post.aggregate([
      {
        $match: {
          isActive: true,
          _id: { $ne: post._id }, // Exclude the current post
        },
      },
      { $sort: { createdAt: -1 } },
      { $limit: 5 },
      {
        $lookup: {
          from: "postcats", // Correct collection name for PostCat
          localField: "categories",
          foreignField: "_id",
          as: "categories",
        },
      },
    ]);

    res.status(200).json({
      success: true,
      post,
      blogs,
      newBlogs,
      blog: post,
    });
  } catch (err) {
    console.log(err);
    res.status(500).json({ message: "Post not found" });
  }
};

exports.updatepostByPut = async (req, res) => {
  try {
    const { id } = req.params;
    const {
      title,
      desc,
      categories,
      isActive,
      index,
      short,
      featureImg,
      slug,
      file,
      isFeature,
      video,
    } = req.body;

    const userId = req.user._id;

    // Normalize file array
    const fileArray = Array.isArray(file)
      ? file.map((f) => ({ path: f.path }))
      : file?.path
      ? [{ path: file.path }]
      : [];

    // Get the existing post
    const oldPost = await Post.findById(id).lean();
    if (!oldPost) {
      return res
        .status(404)
        .json({ success: false, message: "Post not found" });
    }

    // Compute field diffs
    const changes = {};
    const fieldsToCheck = {
      title,
      desc,
      short,
      isActive,
      index,
      slug,
      featureImg,
      isFeature,
      file: fileArray,
      video,
    };

    for (const key in fieldsToCheck) {
      const newVal = fieldsToCheck[key];
      const oldVal = oldPost[key];
      const isEqual = JSON.stringify(newVal) === JSON.stringify(oldVal);

      if (!isEqual) {
        if (key === "desc" || key === "short") {
          changes[key] = { changed: true }; // only mark as changed
        } else {
          changes[key] = { old: oldVal, new: newVal };
        }
      }
    }

    // Compare categories
    const oldCats = (oldPost.categories || [])
      .map((c) => c._id?.toString?.() || c.toString())
      .sort();
    
    // Handle categories - can be array, object with numeric keys, or single value
    let categoriesArray = [];
    if (Array.isArray(categories)) {
      categoriesArray = categories;
    } else if (categories && typeof categories === 'object') {
      // Handle object with numeric keys like { '0': 'id1', '1': 'id2' }
      categoriesArray = Object.values(categories);
    } else if (categories) {
      // Single category
      categoriesArray = [categories];
    }
    
    const newCats = categoriesArray
      .filter(Boolean)
      .map((c) => {
        // Handle both string IDs and objects with _id property
        if (typeof c === 'object' && c._id) {
          return c._id.toString();
        }
        return c.toString();
      })
      .sort();

    console.log('Categories debug:', { 
      originalCategories: categories, 
      categoriesArray: categoriesArray,
      newCats: newCats,
      newCatsTypes: newCats.map(c => typeof c)
    });

    const oldCatsLog = await PostCat.find({ _id: { $in: oldCats } }).select(
      "name"
    );
    const newCatsLog = await PostCat.find({ _id: { $in: newCats } }).select(
      "name"
    );

    if (JSON.stringify(oldCats) !== JSON.stringify(newCats)) {
      changes.categories = {
        old: oldCatsLog.map((cat) => cat.name),
        new: newCatsLog.map((cat) => cat.name),
      };
    }

    // Update post in DB
    const post = await Post.findByIdAndUpdate(
      id,
      {
        $set: {
          ...fieldsToCheck,
          categories: newCats.map(id => new mongoose.Types.ObjectId(id)), // Convert strings to ObjectIds
        },
        $push: {
          revisions: {
            user: userId,
            timestamp: new Date(),
            changes,
          },
        },
      },
      { new: true }
    )
      .populate("user", "username rule")
      .populate("revisions.user", "username");

    res.status(200).json({
      success: true,
      post,
    });
  } catch (err) {
    console.log(err);
    res.status(500).json({
      success: false,
      error: err.message,
    });
  }
};

exports.userUpdatepostByPut = async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user._id;
    const { title, desc, categories, short, featureImg, slug, file, video } =
      req.body;

    const fileArray = Array.isArray(file)
      ? file.map((f) => ({ path: f.path }))
      : file?.path
      ? [{ path: file.path }]
      : [];

    // Get old post for comparison
    const oldPost = await Post.findOne({ _id: id, user: userId }).lean();
    if (!oldPost) {
      return res
        .status(404)
        .json({ success: false, message: "Post not found or unauthorized" });
    }

    const changes = {};
    const fieldsToCheck = {
      title,
      desc,
      short,
      slug,
      featureImg,
      file: fileArray,
      video,
    };

    for (const key in fieldsToCheck) {
      const newVal = fieldsToCheck[key];
      const oldVal = oldPost[key];
      const isEqual = JSON.stringify(newVal) === JSON.stringify(oldVal);

      if (!isEqual) {
        if (key === "desc" || key === "short") {
          changes[key] = { changed: true };
        } else {
          changes[key] = { old: oldVal, new: newVal };
        }
      }
    }

    // Compare categories
    const oldCats = (oldPost.categories || [])
      .map((c) => c._id?.toString?.() || c.toString())
      .sort();
    
    // Handle categories - can be array, object with numeric keys, or single value
    let categoriesArray = [];
    if (Array.isArray(categories)) {
      categoriesArray = categories;
    } else if (categories && typeof categories === 'object') {
      // Handle object with numeric keys like { '0': 'id1', '1': 'id2' }
      categoriesArray = Object.values(categories);
    } else if (categories) {
      // Single category
      categoriesArray = [categories];
    }
    
    const newCats = categoriesArray
      .filter(Boolean)
      .map((c) => {
        // Handle both string IDs and objects with _id property
        if (typeof c === 'object' && c._id) {
          return c._id.toString();
        }
        return c.toString();
      })
      .sort();

    if (JSON.stringify(oldCats) !== JSON.stringify(newCats)) {
      changes.categories = {
        old: oldCats,
        new: newCats,
      };
    }

    // Update post
    const updateData = {
      $set: {
        title,
        desc,
        short,
        slug,
        featureImg,
        file: fileArray,
        categories: newCats.map(id => new mongoose.Types.ObjectId(id)), // Convert strings to ObjectIds
        video,
        // Role-based activation
        isActive: ["editor", "manager", "admin"].includes(req.user.rule),
      },
      $push: {
        revisions: {
          user: userId,
          timestamp: new Date(),
          changes,
        },
      },
    };

    const post = await Post.findOneAndUpdate(
      { _id: id, user: userId },
      updateData,
      { new: true }
    ).populate("revisions.user", "username");

    res.status(200).json({
      post,
      success: true,
    });
  } catch (err) {
    console.error(err);
    res.status(500).json({
      success: false,
      error: err.message,
    });
  }
};

function getLocalPathFromUrl(imageUrl) {
  try {
    const parsed = new URL(imageUrl); // built-in URL parser
    // This gives you just "uploads/media/..."
    return parsed.pathname.replace(/^\/+/, ""); // remove starting slashes
  } catch (e) {
    // Fallback if it's already a relative path
    return imageUrl.replace(/^\/+/, "");
  }
}

exports.postDelete = async (req, res) => {
  try {
    await deletePostAndMedia(req.params.id, req.user);
    res.status(200).json({ success: true, message: "Post has been deleted" });
  } catch (err) {
    res.status(500).json({ success: false, message: err.message });
  }
};


exports.postActiveMulti = async (req, res) => {
  try {
    const { ids, isActive } = req.body;
    let result = ids.map((a) => a);
    const update = await Post.updateMany(
      { _id: { $in: result } },
      {
        $set: {
          isActive: isActive,
        },
      },
      { upsert: true }
    );
    res.status(200).json({
      success: true,
    });
  } catch (err) {
    res.status(404).json({
      error: err,
    });
  }
};

const deletePostAndMedia = async (postId, user) => {
  try {
    console.log(`Starting deletion of post ${postId}`);
    const ability = defineAbilityFor(user);
    const post = await Post.findById(postId).exec();

    if (!post) throw new Error("Post not found");
    ForbiddenError.from(ability).throwUnlessCan("delete", post);

    console.log(`Post found, proceeding with media deletion for ${postId}`);

  // === Delete feature image ===
  if (post.featureImg?.path) {
    const localPath = getLocalPathFromUrl(post.featureImg.path);
    const fullPath = path.join(__dirname, '..', '..', localPath);
    if (fs.existsSync(fullPath)) fs.unlinkSync(fullPath);
  }

  // === Delete images in desc ===
  const imageRegex = /<img[^>]+src="([^">]+)"/g;
  let match;
  while ((match = imageRegex.exec(post.desc)) !== null) {
    const imgUrl = match[1];
    if (imgUrl.includes('/uploads/')) {
      const localPath = getLocalPathFromUrl(imgUrl);
      const fullPath = path.join(__dirname, '..', '..', localPath);
      if (fs.existsSync(fullPath)) fs.unlinkSync(fullPath);
    }
  }

  // === Delete file ===
  if (Array.isArray(post.file)) {
    for (const f of post.file) {
      if (f.path) {
        const localPath = getLocalPathFromUrl(f.path);
        const fullPath = path.join(__dirname, '..', '..', localPath);
        if (fs.existsSync(fullPath)) fs.unlinkSync(fullPath);
      }
    }
  }

  // === Delete videos in post.video ===
  if (post.video && Array.isArray(post.video)) {
    for (const videoUrl of post.video) {
      try {
        if (typeof videoUrl === 'string' && videoUrl.trim()) {
          const match = videoUrl.match(/\/api\/video\/stream\/([^/]+)/);
          if (match && match[1]) {
            const videoId = match[1];
            const videoDoc = await Video.findById(videoId).exec();
            if (videoDoc?.videoPath) {
              const videoPath = getLocalPathFromUrl(videoDoc.videoPath);
              const fullPath = path.join(__dirname, '..', '..', videoPath);
              if (fs.existsSync(fullPath)) fs.unlinkSync(fullPath);
            }
            await Video.findByIdAndDelete(videoId);
          }
        }
      } catch (videoError) {
        console.log(`Error deleting video ${videoUrl}:`, videoError.message);
        // Continue with other videos
      }
    }
  }

  // === Delete video in desc ===
  const videoRegex = /\/api\/video\/stream\/([a-fA-F0-9]{24})/g;
  let videoMatch;
  while ((videoMatch = videoRegex.exec(post.desc)) !== null) {
    const videoId = videoMatch[1];
    const videoDoc = await Video.findById(videoId).exec();
    if (videoDoc?.videoPath) {
      const videoPath = getLocalPathFromUrl(videoDoc.videoPath);
      const fullPath = path.join(__dirname, '..', '..', videoPath);
      if (fs.existsSync(fullPath)) fs.unlinkSync(fullPath);
    }
    await Video.findByIdAndDelete(videoId);
  }

    // Finally, remove the post
    console.log(`Removing post ${postId} from database`);
    await Post.findByIdAndDelete(postId);
    console.log(`Successfully deleted post ${postId}`);
  } catch (error) {
    console.error(`Error in deletePostAndMedia for post ${postId}:`, error);
    throw error;
  }
};


exports.postDeleteMulti = async (req, res) => {
  try {
    const { ids } = req.body;
    const deleted = [];
    const failed = [];

    for (const id of ids) {
      try {
        await deletePostAndMedia(id, req.user);
        deleted.push(id);
      } catch (err) {
        console.error(`Failed to delete post ${id}:`, err.message);
        failed.push({ id, error: err.message });
      }
    }

    res.status(200).json({
      success: true,
      deleted,
      failed,
    });
  } catch (err) {
    res.status(500).json({ success: false, message: err.message });
  }
};


exports.searchBlogs = async (req, res) => {
  try {
    const text = req.query.text?.trim() || ""; // Get the search text
    const titleFilter = text
      ? { title: { $regex: new RegExp(text, "i") } }
      : {}; // Case-insensitive partial match

    const perPage = parseInt(req.query.perPage, 10) || 20;
    const page = parseInt(req.query.page, 10) || 1;
    const blogs = await Post.aggregate([
      { $match: { isActive: true, ...titleFilter } },

      { $skip: (page - 1) * perPage }, // Skip results for pagination
      { $limit: perPage }, // Limit results per page
      {
        $lookup: {
          from: "postcats", // Correct collection name for PostCat
          localField: "categories",
          foreignField: "_id",
          as: "categories",
        },
      },
    ]);
    const newBlogs = await Post.aggregate([
      { $match: { isActive: true } },
      { $sort: { createdAt: -1 } },
      { $limit: 5 },
      {
        $lookup: {
          from: "postcats", // Correct collection name for PostCat
          localField: "categories",
          foreignField: "_id",
          as: "categories",
        },
      },
    ]);
    res.json({
      success: true,
      blogs,
      newBlogs,
    });
  } catch (err) {
    res.status(500).json({
      message: "Please check correctness and try again",
      error: err,
      success: false,
    });
  }
};

exports.authorBlogs = async (req, res) => {
  const { user } = req.params;
  try {
    const perPage = parseInt(req.query.perPage) || 20;
    const page = parseInt(req.query.page) || 1;
    const userId = await User.findOne({ username: user }).select(
      "username rule"
    );
    if (!userId) {
      return res.status(404).json({
        success: false,
        message: "Not found",
      });
    }
    console.log(req.user);
    if (!req.user.rule || req.user.rule === "user") {
      return res.status(404).json({
        success: false,
        message: "Not found",
      });
    }

    const total = await Post.countDocuments({
      $and: [{ isActive: true }, { user: userId._id }],
    });

    const posts = await Post.find({
      $and: [{ isActive: true }, { user: userId._id }],
    })
      .populate("categories")
      .sort({ createdAt: -1 }) // Use -1 for descending order
      .skip(perPage * (page - 1))
      .limit(perPage);
    const newBlogs = await Post.aggregate([
      { $match: { isActive: true } },
      { $sort: { createdAt: -1 } },
      { $limit: 5 },
      {
        $lookup: {
          from: "postcats", // Correct collection name for PostCat
          localField: "categories",
          foreignField: "_id",
          as: "categories",
        },
      },
    ]);
    res.status(200).json({
      success: true,
      total, // Corrected total count
      posts,
      blogs: posts, // Duplicate of `posts`, consider removing
      newBlogs,
      userId,
    });
  } catch (err) {
    console.error("Error fetching posts by category:", err);
    res.status(500).json({
      success: false,
      message: "Can't get products",
      error: err.message,
    });
  }
};
