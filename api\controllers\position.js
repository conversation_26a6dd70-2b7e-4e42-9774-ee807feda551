const Position = require("../models/position");
const Department = require("../models/department");
const Leader = require("../models/leader");

// Get all positions for a specific department
exports.getPositionsByDepartment = async (req, res) => {
  try {
    const { departmentId } = req.params;

    // Validate department exists
    const department = await Department.findById(departmentId);
    if (!department) {
      return res.status(404).json({
        success: false,
        message: "Phòng ban không tồn tại"
      });
    }

    const positions = await Position.find({
      department: departmentId,
      isActive: true
    })
    .populate('department', 'name')
    .populate('createdBy', 'username')
    .sort({ order: 1, level: -1 });

    res.status(200).json({
      success: true,
      data: positions,
      message: "<PERSON><PERSON>y danh sách chức vụ thành công"
    });
  } catch (error) {
    console.error("Error in getPositionsByDepartment:", error);
    res.status(500).json({
      success: false,
      message: "Lỗi server khi lấy danh sách chức vụ"
    });
  }
};

// Get all positions (for admin)
exports.getAllPositions = async (req, res) => {
  try {
    const positions = await Position.find({ isActive: true })
      .populate('department', 'name')
      .populate('createdBy', 'username')
      .sort({ department: 1, order: 1 });

    res.status(200).json({
      success: true,
      data: positions,
      message: "Lấy tất cả chức vụ thành công"
    });
  } catch (error) {
    console.error("Error in getAllPositions:", error);
    res.status(500).json({
      success: false,
      message: "Lỗi server khi lấy danh sách chức vụ"
    });
  }
};

// Get position by ID
exports.getPositionById = async (req, res) => {
  try {
    const { id } = req.params;

    const position = await Position.findById(id)
      .populate('department', 'name')
      .populate('createdBy', 'username');

    if (!position) {
      return res.status(404).json({
        success: false,
        message: "Chức vụ không tồn tại"
      });
    }

    res.status(200).json({
      success: true,
      data: position,
      message: "Lấy thông tin chức vụ thành công"
    });
  } catch (error) {
    console.error("Error in getPositionById:", error);
    res.status(500).json({
      success: false,
      message: "Lỗi server khi lấy thông tin chức vụ"
    });
  }
};

// Create new position for a department
exports.createPosition = async (req, res) => {
  try {
    const { name, description, departmentId, level, permissions, order } = req.body;

    // Validate required fields
    if (!name || !departmentId) {
      return res.status(400).json({
        success: false,
        message: "Tên chức vụ và phòng ban là bắt buộc"
      });
    }

    // Validate department exists
    const department = await Department.findById(departmentId);
    if (!department) {
      return res.status(404).json({
        success: false,
        message: "Phòng ban không tồn tại"
      });
    }

    // Check if position name already exists in this department
    const existingPosition = await Position.findOne({
      name: { $regex: new RegExp(`^${name.trim()}$`, 'i') },
      department: departmentId
    });
    if (existingPosition) {
      return res.status(400).json({
        success: false,
        message: "Tên chức vụ đã tồn tại trong phòng ban này"
      });
    }

    const position = new Position({
      name: name.trim(),
      description: description?.trim() || "",
      department: departmentId,
      permissions: permissions || ["read"],
      level: level || 1,
      order: order || 0,
      createdBy: req.user._id
    });

    await position.save();

    // Populate the response
    await position.populate('department', 'name');
    await position.populate('createdBy', 'username');

    res.status(201).json({
      success: true,
      message: "Tạo chức vụ thành công",
      data: position
    });
  } catch (error) {
    console.error("Error in createPosition:", error);

    if (error.code === 11000) {
      return res.status(400).json({
        success: false,
        message: "Tên chức vụ đã tồn tại trong phòng ban này"
      });
    }

    res.status(500).json({
      success: false,
      message: "Lỗi server khi tạo chức vụ"
    });
  }
};

// Update position
exports.updatePosition = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, description, level, permissions, order } = req.body;

    const position = await Position.findById(id);
    if (!position) {
      return res.status(404).json({
        success: false,
        message: "Chức vụ không tồn tại"
      });
    }

    // Check if new name conflicts with existing positions in the same department
    if (name && name.trim() !== position.name) {
      const existingPosition = await Position.findOne({
        name: { $regex: new RegExp(`^${name.trim()}$`, 'i') },
        department: position.department,
        _id: { $ne: id }
      });
      if (existingPosition) {
        return res.status(400).json({
          success: false,
          message: "Tên chức vụ đã tồn tại trong phòng ban này"
        });
      }
    }

    // Update fields
    if (name) position.name = name.trim();
    if (description !== undefined) position.description = description.trim();
    if (level) position.level = level;
    if (permissions) position.permissions = permissions;
    if (order !== undefined) position.order = order;

    await position.save();

    // Populate the response
    await position.populate('department', 'name');
    await position.populate('createdBy', 'username');

    res.status(200).json({
      success: true,
      message: "Cập nhật chức vụ thành công",
      data: position
    });
  } catch (error) {
    console.error("Error in updatePosition:", error);

    if (error.code === 11000) {
      return res.status(400).json({
        success: false,
        message: "Tên chức vụ đã tồn tại trong phòng ban này"
      });
    }

    res.status(500).json({
      success: false,
      message: "Lỗi server khi cập nhật chức vụ"
    });
  }
};

// Delete position
exports.deletePosition = async (req, res) => {
  try {
    const { id } = req.params;

    const position = await Position.findById(id);
    if (!position) {
      return res.status(404).json({
        success: false,
        message: "Chức vụ không tồn tại"
      });
    }

    // Check if position is default (cannot be deleted)
    if (position.isDefault) {
      return res.status(400).json({
        success: false,
        message: "Không thể xóa chức vụ mặc định"
      });
    }

    // Check if position is being used by any leaders
    const leadersUsingPosition = await Leader.find({ position: id });
    if (leadersUsingPosition.length > 0) {
      return res.status(400).json({
        success: false,
        message: `Không thể xóa chức vụ này vì đang có ${leadersUsingPosition.length} lãnh đạo sử dụng`
      });
    }

    await Position.findByIdAndDelete(id);

    res.status(200).json({
      success: true,
      message: "Xóa chức vụ thành công"
    });
  } catch (error) {
    console.error("Error in deletePosition:", error);
    res.status(500).json({
      success: false,
      message: "Lỗi server khi xóa chức vụ"
    });
  }
};

// Get available permissions
exports.getAvailablePermissions = async (req, res) => {
  try {
    const permissions = [
      { value: "create", label: "Tạo mới" },
      { value: "read", label: "Xem" },
      { value: "update", label: "Cập nhật" },
      { value: "delete", label: "Xóa" },
      { value: "manage_users", label: "Quản lý người dùng" },
      { value: "manage_categories", label: "Quản lý danh mục" },
      { value: "manage_posts", label: "Quản lý bài viết" },
      { value: "view_analytics", label: "Xem thống kê" }
    ];

    res.status(200).json({
      success: true,
      data: permissions,
      message: "Lấy danh sách quyền thành công"
    });
  } catch (error) {
    console.error("Error in getAvailablePermissions:", error);
    res.status(500).json({
      success: false,
      message: "Lỗi server khi lấy danh sách quyền"
    });
  }
};