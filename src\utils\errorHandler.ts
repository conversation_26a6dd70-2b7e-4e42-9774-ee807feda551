/**
 * Utility function to extract meaningful error messages from API responses
 */
export const getErrorMessage = (error: any): string => {
  // If error has detailed validation errors, format them nicely
  if (error?.payload?.errors && typeof error.payload.errors === 'object') {
    const errors = error.payload.errors;
    return formatValidationErrors(errors);
  }
  
  // Try to get message from various possible locations
  const message = error?.payload?.message || 
                 error?.response?.data?.message || 
                 error?.response?.data?.errors ||
                 error?.message ||
                 error?.data?.message;
  
  // If message is still an object, try to extract meaningful info
  if (typeof message === 'object' && message !== null) {
    if (Array.isArray(message)) {
      return message.join(', ');
    }
    // If it's an object with error details
    if (message.message) {
      return message.message;
    }
    // If it's validation errors object
    if (typeof message === 'object') {
      return formatValidationErrors(message);
    }
    // Try to stringify object errors
    return JSON.stringify(message);
  }
  
  // Return the message or a default
  return typeof message === 'string' ? message : '<PERSON><PERSON> lỗi xảy ra, vui lòng thử lại.';
};

/**
 * Handle errors and display appropriate toast messages
 */
export const handleApiError = (error: any, toast: any, defaultMessage: string = 'Có lỗi xảy ra, vui lòng thử lại.'): void => {
  const errorMessage = getErrorMessage(error);
  toast.error(errorMessage || defaultMessage);
};

/**
 * Format validation errors for display
 */
export const formatValidationErrors = (errors: Record<string, string>): string => {
  const errorMessages = Object.entries(errors).map(([field, message]) => {
    // Convert field names to Vietnamese if needed
    const fieldTranslations: Record<string, string> = {
      'email': 'Email',
      'username': 'Tên đăng nhập',
      'password': 'Mật khẩu',
      'confirmPassword': 'Xác nhận mật khẩu',
      'phonenumber': 'Số điện thoại',
      'title': 'Tiêu đề',
      'desc': 'Mô tả',
      'bio': 'Tiểu sử',
      'categories': 'Danh mục',
      '_id': 'ID',
      'name': 'Tên',
      'content': 'Nội dung',
      'slug': 'Đường dẫn',
      'status': 'Trạng thái',
      'url': 'Đường dẫn URL',
      'path': 'Đường dẫn',
      'file': 'Tệp tin',
      'image': 'Hình ảnh'
    };
    
    const fieldName = fieldTranslations[field] || field;
    return `${fieldName}: ${message}`;
  });
  
  return errorMessages.join('\n');
};
