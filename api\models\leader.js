const mongoose = require("mongoose");
const { Schema } = mongoose;

const leaderSchema = new Schema(
  {
    name: { 
      type: String, 
      required: true,
      trim: true 
    },
    position: {
      type: Schema.Types.ObjectId,
      ref: "Position",
      required: true
    },
    department: {
      type: Schema.Types.ObjectId,
      ref: "Department",
      required: true
    },
    bio: {
      type: String,
      required: false,
      trim: true
    },
    experience: {
      type: String,
      required: false,
      trim: true
    },
    education: {
      type: String,
      required: false,
      trim: true
    },
    birthYear: {
      type: Number,
      required: false,
      min: 1900,
      max: new Date().getFullYear()
    },
    hometown: {
      type: String,
      required: false,
      trim: true
    },
    title: {
      type: String,
      required: false,
      trim: true
    },
    achievements: [{
      type: String,
      trim: true
    }],
    phone: { 
      type: String,
      trim: true,
      default: null 
    },
    email: { 
      type: String,
      trim: true,
      match: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
      default: null 
    },
    image: { 
      type: String,
      default: null 
    },
    order: { 
      type: Number, 
      default: 1,
      min: 1 
    },
    isActive: { 
      type: Boolean, 
      default: true 
    },
    slug: {
      type: String,
      unique: true,
      sparse: true
    }
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
  }
);

// Create slug from name before saving
leaderSchema.pre('save', function(next) {
  if (this.isModified('name')) {
    // Create slug from name
    this.slug = this.name
      .toLowerCase()
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '') // Remove diacritics
      .replace(/[^a-z0-9\s-]/g, '') // Remove special chars
      .replace(/\s+/g, '-') // Replace spaces with hyphens
      .replace(/-+/g, '-') // Replace multiple hyphens with single
      .trim('-'); // Remove leading/trailing hyphens
  }
  next();
});

// Index for performance
leaderSchema.index({ isActive: 1, order: 1 });
leaderSchema.index({ position: 1 });
leaderSchema.index({ department: 1 });
leaderSchema.index({ slug: 1 });

module.exports = mongoose.model("Leader", leaderSchema);
