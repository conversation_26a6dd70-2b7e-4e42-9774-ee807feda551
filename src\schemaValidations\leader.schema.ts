import z from "zod";

export const LeaderSchema = z.object({
  id: z.string(),
  name: z.string().trim().min(1, "Tên không được để trống"),
  position: z.string().trim().min(1, "Chức vụ không được để trống"),
  department: z.string().trim().min(1, "Phòng ban không được để trống"),
  bio: z.string().trim().optional(),
  experience: z.string().trim().optional(),
  education: z.string().trim().optional(),
  birthYear: z.number().int().min(1900).max(new Date().getFullYear()).optional().nullable(),
  hometown: z.string().trim().optional(),
  title: z.string().trim().optional(),
  achievements: z.array(z.string()).default([]),
  phone: z.string().optional().nullable(),
  email: z.string().email("<PERSON>ail không đúng định dạng").optional().nullable(),
  image: z.string().optional().nullable(),
  order: z.number().int().positive().default(1),
  isActive: z.boolean().default(true),
  createdAt: z.string(),
  updatedAt: z.string()
});

export const CreateLeaderBodySchema = z.object({
  name: z.string().trim().min(1, "Tên không được để trống"),
  position: z.string().trim().min(1, "Chức vụ không được để trống"),
  department: z.string().trim().min(1, "Phòng ban không được để trống"),
  bio: z.string().trim().optional(),
  experience: z.string().trim().optional(),
  education: z.string().trim().optional(),
  birthYear: z.number().int().min(1900).max(new Date().getFullYear()).optional().nullable(),
  hometown: z.string().trim().optional(),
  title: z.string().trim().optional(),
  achievements: z.array(z.string()).default([]),
  phone: z.string().optional(),
  email: z.string().email("Email không đúng định dạng").optional().or(z.literal("")),
  image: z.string().optional(),
  order: z.number().int().positive().default(1),
  isActive: z.boolean().default(true)
});

export const UpdateLeaderBodySchema = CreateLeaderBodySchema.partial();

export const LeaderListResSchema = z.object({
  data: z.array(LeaderSchema),
  message: z.string()
});

export const LeaderResSchema = z.object({
  data: LeaderSchema,
  message: z.string()
});

export const MessageResSchema = z.object({
  message: z.string()
});

// TypeScript types
export type LeaderType = z.TypeOf<typeof LeaderSchema>;
export type CreateLeaderBodyType = z.TypeOf<typeof CreateLeaderBodySchema>;
export type UpdateLeaderBodyType = z.TypeOf<typeof UpdateLeaderBodySchema>;
export type LeaderListResType = z.TypeOf<typeof LeaderListResSchema>;
export type LeaderResType = z.TypeOf<typeof LeaderResSchema>;
export type MessageResType = z.TypeOf<typeof MessageResSchema>;
