"use client";
import { useState, useEffect } from 'react';
import { Calendar, Search, Filter, Download, Trash2, RefreshCw, AlertCircle, CheckCircle } from 'react-feather';

export default function ScheduleManagePage() {
  const [lichData, setLichData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [filterType, setFilterType] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [dateFilter, setDateFilter] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [lastUpdated, setLastUpdated] = useState(null);
  const [selectedItems, setSelectedItems] = useState([]);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [sortField, setSortField] = useState('ngayLich');
  const [sortDirection, setSortDirection] = useState('asc');
  const [isDeleting, setIsDeleting] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');

  // Fetch data from server
  const fetchDataFromServer = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/schedule');
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const result = await response.json();
      
      if (result && Array.isArray(result.items)) {
        setLichData(result.items);
        setLastUpdated(result.lastUpdated);
      } else {
        setLichData([]);
      }
      
      setError(null);
    } catch (err) {
      setError('Không thể tải dữ liệu từ server. Vui lòng thử lại sau.');
      setLichData([]);
    } finally {
      setLoading(false);
    }
  };

  // Save data to server
  const saveDataToServer = async (data) => {
    try {
      const response = await fetch('/api/schedule', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      
      if (result.success) {
        setLastUpdated(result.lastUpdated);
        return true;
      } else {
        throw new Error(result.error || 'Failed to save data');
      }
    } catch (err) {
      setError('Không thể lưu dữ liệu. Vui lòng thử lại.');
      return false;
    }
  };

  // Parse date for sorting
  const parseDate = (dateStr) => {
    if (!dateStr) return new Date(0);
    
    const parts = dateStr.split('-');
    if (parts.length === 3) {
      const day = parseInt(parts[0]);
      const month = parts[1];
      const year = parseInt(parts[2]);
      
      const monthMap = {
        'Jan': 0, 'Feb': 1, 'Mar': 2, 'Apr': 3, 'May': 4, 'Jun': 5,
        'Jul': 6, 'Aug': 7, 'Sep': 8, 'Oct': 9, 'Nov': 10, 'Dec': 11
      };
      
      const monthNum = monthMap[month] !== undefined ? monthMap[month] : parseInt(month) - 1;
      return new Date(year, monthNum, day);
    }
    
    return new Date(dateStr);
  };

  // Sort data
  const sortData = (data) => {
    return [...data].sort((a, b) => {
      let aValue = a[sortField];
      let bValue = b[sortField];
      
      if (sortField === 'ngayLich') {
        aValue = parseDate(aValue);
        bValue = parseDate(bValue);
      }
      
      if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;
      if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;
      return 0;
    });
  };

  // Filter data
  const filteredData = lichData.filter(item => {
    const matchesSearch = searchTerm === '' || 
      Object.values(item).some(value => 
        value && value.toString().toLowerCase().includes(searchTerm.toLowerCase())
      );
    
    const matchesType = filterType === 'all' || item.loaiAn === filterType;
    
    const matchesDate = dateFilter === '' || item.ngayLich.includes(dateFilter);
    
    return matchesSearch && matchesType && matchesDate;
  });

  const sortedData = sortData(filteredData);

  // Pagination
  const totalPages = Math.ceil(sortedData.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentData = sortedData.slice(startIndex, endIndex);

  // Handle sort
  const handleSort = (field) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  // Handle select all
  const handleSelectAll = (checked) => {
    if (checked) {
      setSelectedItems(currentData.map((_, index) => startIndex + index));
    } else {
      setSelectedItems([]);
    }
  };

  // Handle select item
  const handleSelectItem = (index, checked) => {
    const actualIndex = startIndex + index;
    if (checked) {
      setSelectedItems([...selectedItems, actualIndex]);
    } else {
      setSelectedItems(selectedItems.filter(i => i !== actualIndex));
    }
  };

  // Handle delete selected
  const handleDeleteSelected = async () => {
    if (selectedItems.length === 0) return;
    
    if (!window.confirm(`Bạn có chắc chắn muốn xóa ${selectedItems.length} mục đã chọn?`)) {
      return;
    }

    setIsDeleting(true);
    try {
      const newData = lichData.filter((_, index) => !selectedItems.includes(index));
      const success = await saveDataToServer(newData);
      
      if (success) {
        setLichData(newData);
        setSelectedItems([]);
        setSuccessMessage(`Đã xóa thành công ${selectedItems.length} mục`);
        setTimeout(() => setSuccessMessage(''), 3000);
      }
    } catch (err) {
      setError('Có lỗi xảy ra khi xóa dữ liệu');
    } finally {
      setIsDeleting(false);
    }
  };

  // Handle clear all data
  const handleClearAllData = async () => {
    if (!window.confirm('Bạn có chắc chắn muốn xóa TẤT CẢ dữ liệu? Hành động này không thể hoàn tác!')) {
      return;
    }

    setIsDeleting(true);
    try {
      const response = await fetch('/api/schedule', {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      
      if (result.success) {
        setLichData([]);
        setSelectedItems([]);
        setLastUpdated(result.lastUpdated);
        setSuccessMessage('Đã xóa tất cả dữ liệu thành công');
        setTimeout(() => setSuccessMessage(''), 3000);
      } else {
        throw new Error(result.error || 'Failed to clear data');
      }
    } catch (err) {
      setError('Không thể xóa dữ liệu. Vui lòng thử lại.');
    } finally {
      setIsDeleting(false);
    }
  };

  // Export to CSV
  const exportToCSV = () => {
    const dataToExport = selectedItems.length > 0 
      ? selectedItems.map(index => lichData[index])
      : sortedData;
      
    const headers = ['Ngày lịch', 'Buổi lịch', 'Loại án', 'Phòng xử', 'Nguyên đơn', 'Bị đơn', 'Việc kiện', 'Họ tên', 'Nơi xử'];
    const csvContent = [
      headers.join(','),
      ...dataToExport.map(item => [
        item.ngayLich,
        item.buoiLich,
        item.loaiAn,
        item.phongXu,
        item.nguyenDon,
        item.biDon,
        item.viecKien,
        item.hoTen,
        item.noiXu
      ].map(field => `"${field || ''}"`).join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `lich-xet-xu-${selectedItems.length > 0 ? 'selected-' : ''}${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // Load data when component mounts
  useEffect(() => {
    fetchDataFromServer();
  }, []);

  // Clear success message when data changes
  useEffect(() => {
    if (successMessage) {
      const timer = setTimeout(() => setSuccessMessage(''), 3000);
      return () => clearTimeout(timer);
    }
  }, [successMessage]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 p-6">
        <div className="max-w-7xl mx-auto">
          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="animate-pulse">
              <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
              <div className="space-y-4">
                {[...Array(5)].map((_, i) => (
                  <div key={i} className="h-16 bg-gray-200 rounded"></div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
                <Calendar className="text-blue-600" size={28} />
                Quản Lý Lịch Xét Xử
              </h1>
              <p className="text-gray-600 mt-1">
                Quản lý và xóa dữ liệu lịch xét xử
                {lastUpdated && (
                  <span className="text-sm text-gray-500 ml-2">
                    (Cập nhật lần cuối: {new Date(lastUpdated).toLocaleString('vi-VN')})
                  </span>
                )}
              </p>
            </div>
            
            <div className="flex flex-wrap gap-2">
              <button
                onClick={() => fetchDataFromServer()}
                disabled={loading}
                className="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors flex items-center gap-2 disabled:opacity-50"
              >
                <RefreshCw size={16} className={loading ? 'animate-spin' : ''} />
                Làm mới
              </button>
              <button
                onClick={exportToCSV}
                className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center gap-2"
              >
                <Download size={16} />
                Xuất CSV {selectedItems.length > 0 && `(${selectedItems.length})`}
              </button>
              {selectedItems.length > 0 && (
                <button
                  onClick={handleDeleteSelected}
                  disabled={isDeleting}
                  className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors flex items-center gap-2 disabled:opacity-50"
                >
                  <Trash2 size={16} />
                  Xóa đã chọn ({selectedItems.length})
                </button>
              )}
              <button
                onClick={handleClearAllData}
                disabled={isDeleting || lichData.length === 0}
                className="bg-red-800 text-white px-4 py-2 rounded-lg hover:bg-red-900 transition-colors flex items-center gap-2 disabled:opacity-50"
              >
                <Trash2 size={16} />
                Xóa tất cả
              </button>
            </div>
          </div>
        </div>

        {/* Status Messages */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <div className="flex items-center gap-2 text-red-800">
              <AlertCircle size={16} />
              <span>{error}</span>
            </div>
          </div>
        )}

        {successMessage && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
            <div className="flex items-center gap-2 text-green-800">
              <CheckCircle size={16} />
              <span>{successMessage}</span>
            </div>
          </div>
        )}

        {/* Filters */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <Search size={16} className="inline mr-1" />
                Tìm kiếm
              </label>
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Tìm kiếm..."
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <Filter size={16} className="inline mr-1" />
                Loại án
              </label>
              <select
                value={filterType}
                onChange={(e) => setFilterType(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="all">Tất cả</option>
                <option value="KTST">KTST</option>
                <option value="HCST">HCST</option>
                <option value="HTST">HTST</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <Calendar size={16} className="inline mr-1" />
                Lọc theo ngày
              </label>
              <input
                type="text"
                value={dateFilter}
                onChange={(e) => setDateFilter(e.target.value)}
                placeholder="VD: Nov-22"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Hiển thị
              </label>
              <select
                value={itemsPerPage}
                onChange={(e) => setItemsPerPage(Number(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value={10}>10 mục</option>
                <option value={25}>25 mục</option>
                <option value={50}>50 mục</option>
                <option value={100}>100 mục</option>
              </select>
            </div>
          </div>
        </div>

        {/* Statistics */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{lichData.length}</div>
              <div className="text-sm text-gray-600">Tổng số mục</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{sortedData.length}</div>
              <div className="text-sm text-gray-600">Sau lọc</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">{selectedItems.length}</div>
              <div className="text-sm text-gray-600">Đã chọn</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">
                {lichData.filter(item => item.loaiAn === 'KTST').length}
              </div>
              <div className="text-sm text-gray-600">KTST</div>
            </div>
          </div>
        </div>

        {/* Data Table */}
        <div className="bg-white rounded-lg shadow-sm overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left">
                    <input
                      type="checkbox"
                      checked={currentData.length > 0 && selectedItems.length === currentData.length}
                      onChange={(e) => handleSelectAll(e.target.checked)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                  </th>
                  {[
                    { key: 'ngayLich', label: 'Ngày lịch' },
                    { key: 'buoiLich', label: 'Buổi lịch' },
                    { key: 'loaiAn', label: 'Loại án' },
                    { key: 'phongXu', label: 'Phòng xử' },
                    { key: 'nguyenDon', label: 'Nguyên đơn' },
                    { key: 'biDon', label: 'Bị đơn' },
                    { key: 'viecKien', label: 'Việc kiện' },
                    { key: 'hoTen', label: 'Họ tên' },
                    { key: 'noiXu', label: 'Nơi xử' }
                  ].map((column) => (
                    <th
                      key={column.key}
                      onClick={() => handleSort(column.key)}
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 transition-colors"
                    >
                      <div className="flex items-center gap-1">
                        {column.label}
                        {sortField === column.key && (
                          <span className="text-blue-600">
                            {sortDirection === 'asc' ? '↑' : '↓'}
                          </span>
                        )}
                      </div>
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {currentData.length === 0 ? (
                  <tr>
                    <td colSpan="10" className="px-6 py-12 text-center text-gray-500">
                      <div className="flex flex-col items-center gap-2">
                        <Calendar size={48} className="text-gray-300" />
                        <span>Không có dữ liệu</span>
                      </div>
                    </td>
                  </tr>
                ) : (
                  currentData.map((item, index) => {
                    const actualIndex = startIndex + index;
                    const isSelected = selectedItems.includes(actualIndex);

                    return (
                      <tr
                        key={actualIndex}
                        className={`hover:bg-gray-50 transition-colors ${isSelected ? 'bg-blue-50' : ''}`}
                      >
                        <td className="px-6 py-4">
                          <input
                            type="checkbox"
                            checked={isSelected}
                            onChange={(e) => handleSelectItem(index, e.target.checked)}
                            className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                          />
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{item.ngayLich}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{item.buoiLich}</td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            item.loaiAn === 'KTST' ? 'bg-blue-100 text-blue-800' :
                            item.loaiAn === 'HCST' ? 'bg-green-100 text-green-800' :
                            item.loaiAn === 'HTST' ? 'bg-purple-100 text-purple-800' :
                            'bg-gray-100 text-gray-800'
                          }`}>
                            {item.loaiAn}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{item.phongXu}</td>
                        <td className="px-6 py-4 text-sm text-gray-900 max-w-xs truncate" title={item.nguyenDon}>
                          {item.nguyenDon}
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-900 max-w-xs truncate" title={item.biDon}>
                          {item.biDon}
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-900 max-w-xs truncate" title={item.viecKien}>
                          {item.viecKien}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{item.hoTen}</td>
                        <td className="px-6 py-4 text-sm text-gray-900 max-w-xs truncate" title={item.noiXu}>
                          {item.noiXu}
                        </td>
                      </tr>
                    );
                  })
                )}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
              <div className="flex-1 flex justify-between sm:hidden">
                <button
                  onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                  disabled={currentPage === 1}
                  className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Trước
                </button>
                <button
                  onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                  disabled={currentPage === totalPages}
                  className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Sau
                </button>
              </div>
              <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                  <p className="text-sm text-gray-700">
                    Hiển thị <span className="font-medium">{startIndex + 1}</span> đến{' '}
                    <span className="font-medium">{Math.min(endIndex, sortedData.length)}</span> trong{' '}
                    <span className="font-medium">{sortedData.length}</span> kết quả
                  </p>
                </div>
                <div>
                  <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                    <button
                      onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                      disabled={currentPage === 1}
                      className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Trước
                    </button>

                    {[...Array(totalPages)].map((_, i) => {
                      const page = i + 1;
                      if (
                        page === 1 ||
                        page === totalPages ||
                        (page >= currentPage - 2 && page <= currentPage + 2)
                      ) {
                        return (
                          <button
                            key={page}
                            onClick={() => setCurrentPage(page)}
                            className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                              currentPage === page
                                ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                                : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                            }`}
                          >
                            {page}
                          </button>
                        );
                      } else if (
                        page === currentPage - 3 ||
                        page === currentPage + 3
                      ) {
                        return (
                          <span
                            key={page}
                            className="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700"
                          >
                            ...
                          </span>
                        );
                      }
                      return null;
                    })}

                    <button
                      onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                      disabled={currentPage === totalPages}
                      className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Sau
                    </button>
                  </nav>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
